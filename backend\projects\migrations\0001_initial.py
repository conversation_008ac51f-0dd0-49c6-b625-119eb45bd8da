# Generated by Django 4.2 on 2025-02-01 18:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import projects.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('pdf_file', models.FileField(upload_to=projects.models.get_project_pdf_path)),
                ('evaluation_markdown', models.TextField(blank=True, help_text='Evaluation guidelines in markdown format', null=True)),
                ('points_required', models.IntegerField(default=1)),
                ('level_required', models.IntegerField(default=1)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('track', models.ForeignKey(blank=True, help_text='The track this project belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='projects', to='users.track')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectSubmission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('github_repo', models.URLField(blank=True, null=True)),
                ('zip_file', models.FileField(blank=True, null=True, upload_to=projects.models.get_submission_zip_path)),
                ('submission_type', models.CharField(choices=[('github', 'GitHub Repository'), ('zip', 'ZIP File')], default='github', max_length=10)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_evaluation', 'In Evaluation'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='projects.project')),
                ('submitted_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submitted_projects', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Evaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('comments', models.TextField()),
                ('is_approved', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('evaluator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to=settings.AUTH_USER_MODEL)),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='projects.projectsubmission')),
            ],
        ),
    ]
