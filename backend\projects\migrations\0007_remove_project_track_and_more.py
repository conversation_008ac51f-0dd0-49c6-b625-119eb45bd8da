# Generated by Django 4.2.18 on 2025-05-27 20:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_alter_user_managers_user_phone_number'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('projects', '0006_alter_project_pdf_file_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='project',
            name='track',
        ),
        migrations.RemoveField(
            model_name='projectsubmission',
            name='assigned_evaluator',
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('code', models.CharField(max_length=20, primary_key=True, serialize=False, unique=True)),
                ('name', models.CharField(max_length=150, unique=True)),
                ('description', models.TextField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('educator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='courses', to=settings.AUTH_USER_MODEL)),
                ('track', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='courses', to='users.track')),
            ],
        ),
        migrations.AddField(
            model_name='project',
            name='course',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='projects', to='projects.course'),
        ),
    ]
