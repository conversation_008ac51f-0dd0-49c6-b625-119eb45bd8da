import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Link,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
} from "@mui/material";
import api from "../../services/api";
import { ProjectSubmission, Question } from "../../types/index";
import PictureAsPdfIcon from "@mui/icons-material/PictureAsPdf";
import FolderIcon from "@mui/icons-material/Folder";
import LinkIcon from "@mui/icons-material/Link";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import DoneIcon from "@mui/icons-material/Done";
import CloseIcon from "@mui/icons-material/Close";

interface QuestionResponseProps {
  question: string;
  value: boolean | null;
  onChange: (value: boolean) => void;
}


const QuestionResponse: React.FC<QuestionResponseProps> = ({
  question,
  value,
  onChange,
}) => {
  
  
  return (
    <Box sx={{ mb: 3 }}>
        <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
          {question}
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6}>
          <Button
            fullWidth
            variant="outlined"
            color="success"
            onClick={() => onChange(true)}
            startIcon={<DoneIcon />}
            sx={{
              height: '48px'
            }}
            style={{
              backgroundColor: value === true ? '#4CAF50' : 'transparent'
            }}
          >
            Yes
          </Button>
        </Grid>
        <Grid item xs={6}>
          <Button
            fullWidth
            variant="outlined"
            color="success"
            onClick={() => onChange(false)}
            startIcon={<CloseIcon />}
            sx={{
              height: '48px'
            }}
            style={{
              backgroundColor: value === false ? '#f44336' : 'transparent'
            }}
          >
            No
          </Button>
          </Grid>
        </Grid>
    </Box>
  );
};

const EvaluationPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [submission, setSubmission] = useState<ProjectSubmission | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [answers, setAnswers] = useState<{[key: number]: boolean}>({});
  const [evaluation, setEvaluation] = useState({
    comments: "",
    is_approved: false,
  });

  useEffect(() => {
    fetchSubmission();
  }, [id]);

  const fetchSubmission = async () => {
    try {
      const response = await api.get(`/projects/submissions/${id}/`);
      setSubmission(response.data.submission);
      if (response.data.submission.project.questions) {
        setQuestions(response.data.submission.project.questions);
        const initialAnswers = response.data.submission.project.questions.reduce(
          (acc: {[key: number]: boolean}, question: Question) => {
            acc[question.id] = false;
            return acc;
          },
          {}
        );
        setAnswers(initialAnswers);
      }
      
    } catch (error: any) {
      setError(error.response?.data?.detail || "Failed to load submission");
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (approved: boolean) => {
    if (!evaluation.comments.trim()) {
      setError("Please provide evaluation comments");
      return;
    }

    setSubmitting(true);
    try {
      await api.post(`/projects/evaluate/`, {
        submission_id: id,
        comments: evaluation.comments.trim(),
        is_approved: approved,
        question_responses: Object.entries(answers).map(([id, response]) => ({
          question_id: parseInt(id),
          response
        }))
      });

      navigate("/dashboard");
    } catch (error: any) {
      setError(error.response?.data?.detail || "Failed to submit evaluation");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
        <CircularProgress />
      </Box>
    );
  }

  if (!submission) {
    return <Alert severity="error">Submission not found</Alert>;
  }

  return (
    <Grid container spacing={3}>
      {error && (
        <Grid item xs={12}>
          <Alert severity="error">{error}</Alert>
        </Grid>
      )}

      {/* Project Details */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h5" gutterBottom sx={{ mb: 2 }}>
              {submission.project.title}
            </Typography>
            <Typography variant="body1" paragraph sx={{ mb: 3 }}>
              {submission.project.description}
            </Typography>
            <Box sx={{ display: "flex", gap: 2, flexDirection: 'column' }}>
              <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={<PictureAsPdfIcon />}
                  component={Link}
                  href={submission.project.pdf_file}
                  target="_blank"
                >
                  View Requirements
                </Button>
              </Box>
              
              {/* Show submission based on type */}
              {submission.submission_type === 'github' && submission.github_repo && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>Project URL:</Typography>
                  <Button
                    variant="contained"
                    component={Link}
                    href={submission.github_repo}
                    target="_blank"
                    startIcon={<LinkIcon />}
                    sx={{
                      background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                      '&:hover': {
                        background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
                      },
                      textTransform: 'none',
                      fontSize: '1rem',
                      py: 1.5,
                      px: 4,
                      minWidth: '200px'
                    }}
                  >
                    View Project
                  </Button>
                </Box>
              )}
              
              {submission.submission_type === 'zip' && submission.zip_file && (
                <Box>
                  <Typography variant="subtitle1" gutterBottom>Submitted Folder:</Typography>
                  <Paper
                    elevation={3}
                    sx={{
                      p: 3,
                      cursor: 'pointer',
                      border: theme => `2px solid ${theme.palette.primary.main}`,
                      transition: 'all 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: theme => theme.shadows[6],
                      },
                      maxWidth: '500px'
                    }}
                  >
                    <Link 
                      href={submission.zip_file}
                      target="_blank"
                      download
                      sx={{ textDecoration: 'none' }}
                    >
                      <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="center"
                        gap={2}
                        sx={{
                          minHeight: '120px',
                          justifyContent: 'center',
                          borderRadius: 1,
                          p: 2,
                        }}
                      >
                        <FolderIcon sx={{ 
                          fontSize: 40,
                          color: theme => theme.palette.primary.main
                        }} />
                        <Typography 
                          variant="h6" 
                          align="center"
                          sx={{ 
                            color: 'text.primary',
                            fontWeight: 500
                          }}
                        >
                          Download Project Files
                        </Typography>
                        <Typography 
                          variant="body2" 
                          align="center"
                          sx={{ color: 'text.secondary' }}
                        >
                          Click to download the ZIP file
                        </Typography>
                      </Box>
                    </Link>
                  </Paper>
                </Box>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>

      {/* Submission Details */}
      <Grid item xs={12}>
        <Paper sx={{ p: 3 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" gutterBottom>
              Submission Details
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Typography variant="body1">
                <strong>Submitted by:</strong> {submission.submitted_by.username}
              </Typography>
              <Typography variant="body1">
                <strong>Name:</strong> {submission.submitted_by.first_name} {submission.submitted_by.last_name}
              </Typography>
              <Typography variant="body1">
                <strong>Email:</strong> {submission.submitted_by.email}
              </Typography>
              {submission.submitted_by.phone_number && (
                <Typography variant="body1">
                  <strong>Phone Number:</strong> {submission.submitted_by.phone_number}
                </Typography>
              )}
            </Box>
          </Box>

          {/* Evaluation Questions */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom sx={{ mb: 2 }}>
              Evaluation Criteria
            </Typography>
            {questions.map((question) => (
              <QuestionResponse
                key={question.id}
                question={question.text}
                value={answers[question.id]}
                onChange={(value) =>
                  setAnswers((prev) => ({
                    ...prev,
                    [question.id]: value,
                  }))
                }
              />
            ))}
          </Box>

          <TextField
            fullWidth
            multiline
            rows={6}
            label="Evaluation Comments"
            value={evaluation.comments}
            onChange={(e) => {
              const newValue = e.target.value.slice(0, 500);
              setEvaluation({ ...evaluation, comments: newValue });
            }}
            error={Boolean(error && !evaluation.comments.trim())}
            helperText={
              error && !evaluation.comments.trim() 
                ? error 
                : `${evaluation.comments.length}/500 characters`
            }
            inputProps={{ maxLength: 500 }}
            sx={{ mb: 3 }}
          />

          <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
            <Button
              variant="contained"
              color="success"
              startIcon={<CheckCircleIcon />}
              onClick={() => handleSubmit(true)}
              disabled={submitting}
              
            >
              Approve
            </Button>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default EvaluationPage;