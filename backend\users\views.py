from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth import login, logout
from .models import User, Track
from .serializers import (
    UserSerializer, UserRegistrationSerializer,
    UserLoginSerializer, TrackSerializer
)
from rest_framework.authtoken.models import Token # For main auth
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from rest_framework.permissions import AllowAny
from .email_utils import send_registration_email, send_admin_notification, send_approval_confirmation_email, send_password_reset_email
from .utils import validate_password
import logging
import uuid
from django.db.models import Count
from django.contrib.auth import (get_backends)
from django.middleware.csrf import get_token
from django.core.mail import send_mail
from django.conf import settings
import jwt  # Only for password reset functionality
from datetime import datetime, timedelta
import os
from .throttles import PasswordResetRequestThrottle, PasswordResetVerifyThrottle, EmailThrottle

logger = logging.getLogger(__name__)

# Create your views here.
class TrackListView(generics.ListAPIView):
    queryset = Track.objects.all()
    serializer_class = TrackSerializer
    permission_classes = [AllowAny]

class UserLoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        return Response({'csrfToken': get_token(request)})

    def post(self, request):
        try:
            serializer = UserLoginSerializer(data=request.data)
            if not serializer.is_valid():
                # Return 200 status with error message instead of 400
                return Response({
                    'success': False,
                    'error': 'Invalid email or password'
                }, status=status.HTTP_200_OK)

            user = serializer.validated_data

            # Get or create token
            token, _ = Token.objects.get_or_create(user=user)

            # Login user
            backend = get_backends()[0]
            user.backend = f"{backend.__module__}.{backend.__class__.__name__}"
            login(request, user)

            return Response({
                'success': True,
                'token': token.key,
                'user': UserSerializer(user).data
            }, status=status.HTTP_200_OK)
        except Exception as e:
            # Return 200 status with error message
            return Response({
                'success': False,
                'error': 'An error occurred during login. Please try again.'
            }, status=status.HTTP_200_OK)

class UserLogoutView(APIView):
    def post(self, request):
        logout(request)
        return Response(status=status.HTTP_200_OK)

class UserProfileView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    
    def get(self, request):
        return Response(UserSerializer(request.user, context={'request': request}).data)

    def patch(self, request):
        serializer = UserSerializer(
            request.user, # The instance of the User model to update
            data=request.data, # The data to update the User model with
            partial=True, # Allow partial updates (for PATCH requests), otherwise it will require all fields to be present
            context={
                'request': request, # Extra context for the serializer, Request object is passed to the serializer as it is needed for generating the URL for the profile picture
            }
        )

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def put(self, request):
        # Handle PUT requests for updating current course
        serializer = UserSerializer(
            request.user,
            data=request.data,
            partial=True,
            context={'request': request}
        )

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserApprovalView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, code):
        try:
            # Instead, handle as appropriate (e.g., return error or skip)
            return Response({'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response(
                {'error': 'An error occurred while processing your request'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class AdminApprovalView(APIView):
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, code):
        try:
            user = User.objects.get(admin_approval_code=code)
            if not user:
                return Response({'message': 'User not found'}, status=status.HTTP_400_BAD_REQUEST)
            
            was_approved = user.is_approved  # Store previous state
            user.is_approved = True
            user.save()
            
            # Only send approval email if this admin approval was the last step
            if user.is_email_verified and not was_approved:
                send_approval_confirmation_email(user)
                message = 'User approved successfully and approval email sent'
            else:
                message = 'User approved successfully, waiting for email verification'
                
            return Response({'message': message})
        except Exception as e:
            if type(e) is NotUniqueError:
                return Response({'message': 'User already approved'}, status=status.HTTP_400_BAD_REQUEST)
            return Response(
                {'error': 'An error occurred while processing your request'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class RegisterView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request):
        try:
            serializer = UserRegistrationSerializer(data=request.data)
            if serializer.is_valid():
                # Create user
                user = serializer.save()
                
                # Generate approval codes
                if user.user_type == 'E':
                    user.admin_approval_code = str(uuid.uuid4())
                    user.save()

                # Send emails
                try:
                    send_registration_email(user)
                    if user.user_type == 'E':
                        send_admin_notification(user)
                except Exception as e:
                    logger.error(f"Failed to send registration emails: {str(e)}")
                    # Continue anyway, don't fail the registration

                # Create auth token
                try:
                    token, created = Token.objects.get_or_create(user=user)
                except Exception as e:
                    logger.error(f"Failed to create token: {str(e)}")
                    return Response({
                        'user': UserSerializer(user).data,
                        'message': 'Registration successful but token creation failed. Please login separately.'
                    }, status=status.HTTP_201_CREATED)

                return Response({
                    'user': UserSerializer(user).data,
                    'token': token.key,
                    'message': 'Registration successful. Please check your email to confirm your registration.'
                }, status=status.HTTP_201_CREATED)
            else:
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            # Error during registration
            logger.error(f"Registration failed: {str(e)}")
            return Response({'error': 'An unexpected error occurred'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ForgotPasswordView(APIView):
    permission_classes = []
    throttle_classes = [PasswordResetRequestThrottle, EmailThrottle]

    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)
            send_password_reset_email(user)
            return Response({'message': 'Password reset email sent'}, status=status.HTTP_200_OK)
        except User.DoesNotExist:
            # For security, don't reveal if email exists
            return Response({'message': 'If an account exists with this email, a password reset link will be sent'}, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Failed to process password reset request: {str(e)}")
            return Response(
                {'error': 'An error occurred while processing your request'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ResetPasswordView(APIView):
    permission_classes = []
    throttle_classes = [PasswordResetVerifyThrottle]

    def post(self, request, token):
        try:
            # Verify token
            payload = jwt.decode(token, str(settings.SECRET_KEY), algorithms=['HS256'])
            
            # Verify token type
            if payload.get('type') != 'password_reset':
                return Response({'error': 'Invalid reset link'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get user
            user = User.objects.get(id=payload['user_id'])

            # Get new password
            new_password = request.data.get('new_password')
            if not new_password:
                return Response({'error': 'New password is required'}, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate password
            is_valid, error_message = validate_password(new_password, {
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name
            })
            if not is_valid:
                return Response({'error': error_message}, status=status.HTTP_400_BAD_REQUEST)

            # Update password
            user.set_password(new_password)
            user.save()

            # Invalidate all user's tokens
            Token.objects.filter(user=user).delete()

            # Log the successful password reset
            logger.info(f"Password reset successful for user {user.email}")

            return Response({'message': 'Password updated successfully'}, status=status.HTTP_200_OK)
        except jwt.ExpiredSignatureError:
            return Response({'error': 'Reset link has expired'}, status=status.HTTP_400_BAD_REQUEST)
        except (jwt.InvalidTokenError, User.DoesNotExist):
            return Response({'error': 'Invalid reset link'}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Failed to reset password: {str(e)}")
            return Response({'error': 'An error occurred while resetting your password'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
