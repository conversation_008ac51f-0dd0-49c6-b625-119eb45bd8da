import React, { useState, useCallback } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  Box,
  Typography,
  Chip,
  Alert,
  InputAdornment,
  Paper,
  IconButton,
  CircularProgress,
  LinearProgress,
} from "@mui/material";
import LinkIcon from '@mui/icons-material/Link';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';
import { useDropzone } from 'react-dropzone';
import { Project } from "@/types";
import { useAuth } from "@/contexts/AuthContext";
import api from "@/services/api";
import axios, { CancelTokenSource, AxiosProgressEvent } from 'axios';


interface ProjectViewProps {
  project: Project;
  onSubmit?: () => void;
  submissionStatus?: string;
  onSubmitSuccess?: () => Promise<void>;
}

// Type for error message that can be string or JSX
type ErrorMessage = string | JSX.Element;

const ProjectView: React.FC<ProjectViewProps> = ({
  project,
  onSubmit,
  submissionStatus,
  onSubmitSuccess,
}) => {
  const { user } = useAuth();
  const [openSubmitDialog, setOpenSubmitDialog] = useState(false);
  const [submissionType, setSubmissionType] = useState<'github' | 'zip'>('github');
  const [githubRepo, setGithubRepo] = useState("");
  const [zipFile, setZipFile] = useState<File | null>(null);
  const [error, setError] = useState<ErrorMessage | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadState, setUploadState] = useState<'idle' | 'uploading' | 'processing'>('idle');
  const [cancelTokenSource, setCancelTokenSource] = useState<CancelTokenSource | null>(null);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB in bytes

    if (!file || !file.name.toLowerCase().endsWith('.zip')) {
      setError('Please upload a valid ZIP file');
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      setError('File size must be less than 50MB');
      return;
    }

    setZipFile(file);
    setSubmissionType('zip');
    setError(null);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/zip': ['.zip'],
    },
    maxFiles: 1,
    multiple: false,
  });

  const handleCancel = async () => {
    if (cancelTokenSource) {
      try {
        // Cancel the ongoing upload
        cancelTokenSource.cancel('Upload cancelled by user');

        // Notify server to clean up
        try {
          await api.delete('/projects/cancel-upload/', {
            data: { project_id: project.id },
            headers: {
              'X-Cancel-Upload': 'true',
              'X-Requested-With': 'XMLHttpRequest',
            }
          });
        } catch (cleanupErr) {
          console.error('Cleanup error:', cleanupErr);
        }
      } catch (err) {
        // Handle cancel error silently
      } finally {
        // Reset all states
        setCancelTokenSource(null);
        setUploadProgress(0);
        setUploadState('idle');
        setSubmitting(false);
        setError('Upload cancelled by user');
        setOpenSubmitDialog(false);
        setZipFile(null);

        // Clear file inputs
        const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }

        // Clear dropzone
        if (getRootProps().ref.current) {
          getRootProps().ref.current.value = '';
        }
      }
    }
  };

  const handleSubmit = async () => {
    // Start file upload process
    // Clean up any existing cancel token source
    if (cancelTokenSource) {
      cancelTokenSource.cancel('New upload started');
      setCancelTokenSource(null);
    }
    setError(null);
    setUploadProgress(0);
    setUploadState('uploading');
    setSubmitting(true);

    // Set initial progress to show the upload has started
    setTimeout(() => setUploadProgress(1), 0);

    try {
      const formData = new FormData();
      formData.append('project_id', project.id.toString());
      formData.append('submission_type', submissionType);
      
      if (submissionType === 'github') {
        formData.append('github_repo', githubRepo);
      } else {
        if (!zipFile) {
          setError("Please select a ZIP file");
          return;
        }
        formData.append('zip_file', zipFile);
      }

      // Create a new cancel token source
      const source = axios.CancelToken.source();
      setCancelTokenSource(source);

      try {

        await api.post("/projects/submit/", formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            'X-Requested-With': 'XMLHttpRequest',
          },
          cancelToken: source.token,
          timeout: 0,
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
          onUploadProgress: (progressEvent: AxiosProgressEvent) => {
            if (!progressEvent.total) return;
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            // Ensure progress stays between 1 and 99 during upload
            const adjustedProgress = Math.min(Math.max(percentCompleted, 1), 99);
            setUploadProgress(adjustedProgress);

            if (percentCompleted >= 99) {
              setUploadState('processing');
              // Keep progress at 99% during processing to show it's not complete
              setUploadProgress(99);
            }
          }
        });
      } catch (error: any) {
        if (axios.isCancel(error)) {
          setError('Upload cancelled by user');
          return;
        }
        throw error;
      }
      
      setOpenSubmitDialog(false);
      setGithubRepo("");
      setZipFile(null);
      if (onSubmit) onSubmit();

      if (onSubmitSuccess) {
        await onSubmitSuccess();
      }
    } catch (error: any) {
      // Handle network errors
      if (!error.response) {
        setError("Oops! We couldn't reach our servers. Please check your internet connection and try again.");
        return;
      }

      // Get error details
      const status = error.response?.status;
      const errorMessage = error.response?.data?.detail || error.response?.data?.message || '';
      const lowerErrorMsg = errorMessage.toLowerCase();
      
      // Handle 500 errors specifically
      if (status === 500) {
        setError(
          <>
            <Typography variant="body1" gutterBottom>
              Server Error: We're having trouble processing your submission.
            </Typography>
            <Typography variant="body2" color="error.main">
              Error details: {errorMessage || 'Internal Server Error'}
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Please try again in a few minutes. If the problem persists, contact support.
            </Typography>
          </>
        );
        return;
      }
      
      // Handle specific error messages
      if (lowerErrorMsg.includes("points")) {
        setError("Looks like you need more evaluation points for this project. Try completing some easier projects first to earn more points!");
      } else if (lowerErrorMsg.includes("level")) {
        setError("This project requires a higher level. Complete some easier projects first to level up!");
      } else if (lowerErrorMsg.includes("url")) {
        setError("Please provide a valid website URL (e.g., https://google.com)");
      } else if (lowerErrorMsg.includes("file") || lowerErrorMsg.includes("zip")) {
        setError("There was a problem with your ZIP file. Please ensure it's a valid ZIP file and try again.");
      } else if (status === 413) {
        setError("The file is too large. Please ensure your ZIP file is under 50MB.");
      } else {
        // Show the actual error message from the backend
        setError(
          <>
            <Typography variant="body1" gutterBottom>
              {errorMessage || `Something went wrong with your ${submissionType === 'github' ? 'URL' : 'file'} submission.`}
            </Typography>
            {!errorMessage && (
              <>
                {submissionType === 'github' ? (
                  <Typography variant="body2">
                    Please ensure your URL is valid and accessible (e.g., https://google.com)
                  </Typography>
                ) : (
                  <Typography variant="body2">
                    Please ensure your ZIP file is valid and contains the correct project files.
                  </Typography>
                )}
              </>
            )}
          </>
        );
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleViewPdf = () => {
    window.open(project.pdf_file, "_blank");
  };

  const getSubmitButtonText = () => {
    if (submissionStatus === "pending") return "Pending Evaluation";
    if (submissionStatus === "completed") return "Completed";
    if (submissionStatus === "failed") return "Submit Again";
    return "Submit Project";
  };

  const isDisabled = () => {
    // If project cannot be submitted or no user, disable submission
    if (!project.can_submit || !user) {
      return true;
    }

    // Only allow submission if status is undefined (new submission) or failed
    return submissionStatus === "pending" || submissionStatus === "completed";
  };

  return (
    <Box>
      <Typography variant="h6" color="primary.main" gutterBottom>
        {project.title}
      </Typography>
      <Typography variant="body1" sx={{ mb: 2 }}>
        {project.description}
      </Typography>
      <Box sx={{ display: "flex", gap: 2, alignItems: "center", mb: 2 }}>
        <Chip
          label={`Level ${project.level_required || 1} Required`}
          color={
            user?.level && user.level >= (project.level_required || 1)
              ? "success"
              : "error"
          }
          variant="outlined"
        />
        <Chip
          label={`${project.points_required} Points Required`}
          color={
            user?.points && user.points >= project.points_required
              ? "success"
              : "error"
          }
          variant="outlined"
        />
      </Box>
      <Box sx={{ display: "flex", gap: 2 }}>
        <Button variant="outlined" onClick={handleViewPdf}>
          View PDF
        </Button>
        {submissionStatus && submissionStatus !== "failed" ? (
          <Chip
            label={`Status: ${submissionStatus}`}
            color={
              submissionStatus === "pending"
                ? "warning"
                : submissionStatus === "completed"
                ? "success"
                : "default"
            }
          />
        ) : (
          <Button
            variant="contained"
            onClick={() => setOpenSubmitDialog(true)}
            disabled={isDisabled()}
            sx={{
              background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
              "&:hover": {
                background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
              },
            }}
          >
            {getSubmitButtonText()}
          </Button>
        )}
      </Box>

      <Dialog
        open={openSubmitDialog}
        maxWidth="md"
        fullWidth
        onClose={() => {
          if (!submitting) {
            setOpenSubmitDialog(false);
            setError(null);
            setGithubRepo("");
            setZipFile(null);
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            Submit Project
            <IconButton
              onClick={() => {
                if (!submitting) {
                  setOpenSubmitDialog(false);
                  setError(null);
                  setGithubRepo("");
                  setZipFile(null);
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <Typography variant="body1" gutterBottom>
              Choose how you want to submit your project:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Button
                variant={submissionType === 'github' ? 'contained' : 'outlined'}
                onClick={() => setSubmissionType('github')}
                startIcon={<LinkIcon />}
              >
                Project URL
              </Button>
              <Button
                variant={submissionType === 'zip' ? 'contained' : 'outlined'}
                onClick={() => setSubmissionType('zip')}
                startIcon={<CloudUploadIcon />}
              >
                Upload ZIP
              </Button>
            </Box>
            {submissionType === 'github' ? (
              <TextField
                fullWidth
                label="Project URL"
                value={githubRepo}
                onChange={(e) => setGithubRepo(e.target.value)}
                error={!!error}
                placeholder="Enter your project URL"
                disabled={submitting}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <LinkIcon />
                    </InputAdornment>
                  ),
                }}
              />
            ) : (
              <Paper
                elevation={submissionType === 'zip' ? 3 : 1}
                sx={{
                  flex: 1,
                  p: 3,
                  cursor: 'pointer',
                  border: theme => `2px solid ${submissionType === 'zip' ? theme.palette.primary.main : 'transparent'}`,
                  '&:hover': {
                    elevation: 3,
                  },
                }}
                {...getRootProps()}
              >
                <input {...getInputProps()} />
                <Box
                  display="flex"
                  flexDirection="column"
                  alignItems="center"
                  gap={2}
                  sx={{
                    minHeight: '200px',
                    justifyContent: 'center',
                    border: '2px dashed #ccc',
                    borderRadius: 1,
                    p: 3,
                    backgroundColor: isDragActive ? 'action.hover' : 'transparent',
                  }}
                >
                  <CloudUploadIcon sx={{ fontSize: 40 }} />
                  <Typography variant="h6" align="center">
                    {isDragActive
                      ? "Drop the ZIP file here"
                      : zipFile
                      ? zipFile.name
                      : "Drag & drop a ZIP file here or click to select"}
                  </Typography>
                  {zipFile && (
                    <Button
                      startIcon={<CloseIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setZipFile(null);
                      }}
                    >
                      Remove file
                    </Button>
                  )}
                </Box>
              </Paper>
            )}
          </Box>

          {error && (
            <Alert 
              severity="error" 
              sx={{ 
                mt: 2,
                '& .MuiAlert-message': {
                  width: '100%'
                }
              }}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => setError(null)}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
            >
              {error}
            </Alert>
          )}

          {submitting && submissionType === 'zip' && (
            <Box sx={{ width: '100%', mt: 2 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ width: '100%', mr: 1 }}>
                    <LinearProgress 
                      variant={uploadState === 'processing' ? 'indeterminate' : 'determinate'}
                      value={uploadProgress} 
                    />
                  </Box>
                  <Box sx={{ minWidth: 35 }}>
                    {uploadState === 'processing' ? (
                      <Typography variant="body2" color="text.secondary">Processing...</Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">{`${uploadProgress}%`}</Typography>
                    )}
                  </Box>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {uploadState === 'uploading' ? 'Uploading file...' : 'Processing on server...'}
                </Typography>
              </Box>
              {uploadState === 'uploading' && (
                <Button
                  onClick={handleCancel}
                  color="error"
                  variant="outlined"
                  size="small"
                  sx={{ mt: 1 }}
                  startIcon={<CloseIcon />}
                >
                  Cancel Upload
                </Button>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => {
              if (!submitting) {
                setOpenSubmitDialog(false);
                setError(null);
                setGithubRepo("");
                setZipFile(null);
              }
            }}
            disabled={submitting}
          >
            Close
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={submitting || (submissionType === 'github' && !githubRepo) || (submissionType === 'zip' && !zipFile)}
            startIcon={submitting ? <CircularProgress size={20} /> : undefined}
          >
            {submitting ? "Uploading..." : "Submit"}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProjectView;
