import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  CheckCircle as EnrolledIcon,
} from '@mui/icons-material';

export interface EnrolledStudent {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  level?: number;
  exp?: number;
}

interface EnrolledStudentCardProps {
  student: EnrolledStudent;
}

const EnrolledStudentCard: React.FC<EnrolledStudentCardProps> = ({ student }) => {
  const theme = useTheme();

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getStudentName = () => {
    return `${student.first_name} ${student.last_name}`.trim();
  };

  return (
    <Card
      sx={{
        background: 'rgba(17, 34, 64, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: `0 8px 25px rgba(100, 255, 218, 0.1)`,
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {/* Header with student info */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.secondary.main,
              mr: 2,
              width: 48,
              height: 48,
            }}
          >
            {getInitials(student.first_name, student.last_name)}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ color: 'text.primary', mb: 0.5 }}>
              {getStudentName()}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EmailIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {student.email}
              </Typography>
            </Box>
          </Box>
          <Chip
            label="Enrolled"
            size="small"
            icon={<EnrolledIcon sx={{ fontSize: 16 }} />}
            sx={{
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              color: theme.palette.success.main,
            }}
          />
        </Box>

        {/* Student stats */}
        {(student.level !== undefined || student.exp !== undefined) && (
          <Box 
            sx={{ 
              display: 'flex', 
              gap: 2, 
              p: 2, 
              backgroundColor: 'rgba(123, 137, 244, 0.05)', 
              borderRadius: 1,
              mt: 2 
            }}
          >
            {student.level !== undefined && (
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                  Level
                </Typography>
                <Typography variant="h6" sx={{ color: theme.palette.primary.main, fontWeight: 600 }}>
                  {student.level}
                </Typography>
              </Box>
            )}
            {student.exp !== undefined && (
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
                  Experience
                </Typography>
                <Typography variant="h6" sx={{ color: theme.palette.secondary.main, fontWeight: 600 }}>
                  {student.exp}
                </Typography>
              </Box>
            )}
          </Box>
        )}

        {/* Username info */}
        <Box sx={{ display: 'flex', alignItems: 'center', mt: 2, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <PersonIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Username: {student.username}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default EnrolledStudentCard;
