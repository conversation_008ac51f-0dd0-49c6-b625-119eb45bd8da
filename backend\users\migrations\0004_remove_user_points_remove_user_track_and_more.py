# Generated by Django 4.2.18 on 2025-05-27 20:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0007_remove_project_track_and_more'),
        ('users', '0003_alter_user_managers_user_phone_number'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='points',
        ),
        migrations.RemoveField(
            model_name='user',
            name='track',
        ),
        migrations.RemoveField(
            model_name='user',
            name='zone',
        ),
        migrations.AddField(
            model_name='user',
            name='enrolled_courses',
            field=models.ManyToManyField(blank=True, related_name='enrolled_students', to='projects.course'),
        ),
        migrations.AddField(
            model_name='user',
            name='exp',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('S', 'Student'), ('E', 'Educator')], default='S'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='user',
            name='username',
            field=models.Char<PERSON>ield(max_length=50, unique=True),
        ),
    ]
