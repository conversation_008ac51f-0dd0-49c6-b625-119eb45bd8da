import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Box,
  Container,
  useScrollTrigger,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemText,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface Props {
  window?: () => Window;
}

const LandingHeader: React.FC<Props> = (props) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);

  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 0,
    target: props.window ? props.window() : undefined,
  });

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    element?.scrollIntoView({ behavior: 'smooth' });
    if (mobileOpen) handleDrawerToggle();
  };

  const navItems = [
    { name: 'Features', id: 'features' },
    { name: 'Tracks', id: 'tracks' },
    { name: 'FAQ', id: 'faq' },
  ];

  const drawer = (
    <Box sx={{ textAlign: 'center', py: 2 }}>
      <Typography
        variant="h6"
        sx={{
          mb: 2,
          background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        DevSpace
      </Typography>
      <List>
        {navItems.map((item) => (
          <ListItem key={item.id} disablePadding>
            <ListItemText>
              <Button
                fullWidth
                onClick={() => scrollToSection(item.id)}
                sx={{ color: '#8892b0' }}
              >
                {item.name}
              </Button>
            </ListItemText>
          </ListItem>
        ))}
        {!user ? (
          <>
            <ListItem disablePadding>
              <ListItemText>
                <Button
                  fullWidth
                  onClick={() => {
                    navigate('/login');
                    handleDrawerToggle();
                  }}
                  sx={{ color: '#64FFDA' }}
                >
                  Login
                </Button>
              </ListItemText>
            </ListItem>
            <ListItem disablePadding>
              <ListItemText>
                <Button
                  fullWidth
                  onClick={() => {
                    navigate('/register');
                    handleDrawerToggle();
                  }}
                  sx={{
                    background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                    color: '#fff',
                    '&:hover': {
                      background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
                    },
                  }}
                >
                  Get Started
                </Button>
              </ListItemText>
            </ListItem>
          </>
        ) : (
          <ListItem disablePadding>
            <ListItemText>
              <Button
                fullWidth
                onClick={() => {
                  navigate('/dashboard');
                  handleDrawerToggle();
                }}
                sx={{
                  background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                  color: '#fff',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
                  },
                }}
              >
                Dashboard
              </Button>
            </ListItemText>
          </ListItem>
        )}
      </List>
    </Box>
  );

  return (
    <>
      <AppBar
        position="fixed"
        elevation={trigger ? 4 : 0}
        sx={{
          background: trigger ? '#0a192f' : 'transparent',
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s',
        }}
      >
        <Container maxWidth="lg">
          <Toolbar disableGutters>
            <Typography
              variant="h6"
              component="div"
              onClick={() => navigate('/')}
              sx={{
                flexGrow: 1,
                cursor: 'pointer',
                fontWeight: 700,
                background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              DevSpace
            </Typography>

            {isMobile ? (
              <IconButton
                color="inherit"
                aria-label="open drawer"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{ ml: 2 }}
              >
                <MenuIcon />
              </IconButton>
            ) : (
              <Box sx={{ display: 'flex', gap: 3, alignItems: 'center' }}>
                {navItems.map((item) => (
                  <Button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    sx={{ color: '#8892b0' }}
                  >
                    {item.name}
                  </Button>
                ))}
                {!user ? (
                  <>
                    <Button
                      onClick={() => navigate('/login')}
                      sx={{
                        color: '#64FFDA',
                      }}
                    >
                      Login
                    </Button>
                    <Button
                      variant="contained"
                      onClick={() => navigate('/register')}
                      sx={{
                        background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                        '&:hover': {
                          background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
                        },
                      }}
                    >
                      Get Started
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="contained"
                    onClick={() => navigate('/dashboard')}
                    sx={{
                      background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
                      '&:hover': {
                        background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
                      },
                    }}
                  >
                    Dashboard
                  </Button>
                )}
              </Box>
            )}
          </Toolbar>
        </Container>
      </AppBar>

      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better mobile performance
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 240,
            bgcolor: '#0a192f',
          },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

export default LandingHeader; 