import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Paper,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Button,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import BackToHome from '@/components/BackToHome';
import api from '@/services/api';

const AdminApproval: React.FC = () => {
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    const approveUser = async () => {
      try {
        const response = await api.get(`/users/admin-approve/${code}/`);
        setStatus('success');
        setMessage(response.data.message);
      } catch (error: any) {
        setStatus('error');
        setMessage(error.response?.data?.error || 'Approval failed');
      }
    };

    approveUser();
  }, [code]);

  return (
    <Container component="main" maxWidth="sm">
      <BackToHome />
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: '100%', textAlign: 'center' }}>
          <Typography component="h1" variant="h4" gutterBottom>
            User Approval
          </Typography>

          {status === 'loading' && (
            <Box sx={{ mt: 4, mb: 4 }}>
              <CircularProgress />
              <Typography sx={{ mt: 2 }}>Processing approval...</Typography>
            </Box>
          )}

          {status === 'success' && (
            <>
              <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main', mb: 2 }} />
              <Alert severity="success" sx={{ mb: 3 }}>
                {message || 'User approved successfully!'}
              </Alert>
              <Typography variant="body1" sx={{ mb: 3 }}>
                The user has been approved and can now access the platform.
              </Typography>
              <Button
                variant="contained"
                color="primary"
                onClick={() => navigate('/')}
              >
                Back to Home
              </Button>
            </>
          )}

          {status === 'error' && (
            <>
              <ErrorIcon sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />
              <Alert severity="error" sx={{ mb: 3 }}>
                {message}
              </Alert>
              <Button
                variant="contained"
                color="primary"
                onClick={() => navigate('/')}
              >
                Back to Home
              </Button>
            </>
          )}
        </Paper>
      </Box>
    </Container>
  );
};

export default AdminApproval; 