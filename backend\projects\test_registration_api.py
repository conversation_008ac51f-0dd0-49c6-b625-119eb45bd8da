import pytest
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from projects.models import Course, CourseRegistrationRequest, Track
from users.models import User

User = get_user_model()


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def track():
    return Track.objects.create(
        name="Web Development",
        description="Web Development Track"
    )


@pytest.fixture
def student():
    return User.objects.create_user(
        username='student_test',
        email='<EMAIL>',
        password='TestPass123',
        user_type='S',
        first_name='Test',
        last_name='Student'
    )


@pytest.fixture
def educator():
    return User.objects.create_user(
        username='educator_test',
        email='<EMAIL>',
        password='TestPass123',
        user_type='E',
        first_name='Test',
        last_name='Educator'
    )


@pytest.fixture
def course(track, educator):
    return Course.objects.create(
        code="TEST101",
        name="Test Course",
        description="A test course for registration",
        track=track,
        educator=educator
    )


@pytest.fixture
def pending_registration(student, course):
    return CourseRegistrationRequest.objects.create(
        student=student,
        course=course,
        status='pending'
    )


@pytest.mark.django_db
class TestCourseRegisterView:
    """Test course registration endpoint"""
    
    def test_student_can_register_for_course(self, api_client, student, course):
        """Test that a student can register for a course"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert CourseRegistrationRequest.objects.filter(
            student=student, 
            course=course, 
            status='pending'
        ).exists()
        
        # Check response data
        assert response.data['student']['id'] == student.id
        assert response.data['course']['code'] == course.code
        assert response.data['status'] == 'pending'
    
    def test_duplicate_registration_prevented(self, api_client, student, course):
        """Test that duplicate registration requests are prevented"""
        # Create initial registration
        CourseRegistrationRequest.objects.create(
            student=student,
            course=course,
            status='pending'
        )
        
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert 'already requested' in response.data['detail']
    
    def test_already_enrolled_student_cannot_register(self, api_client, student, course):
        """Test that already enrolled students cannot register again"""
        # Create approved registration
        CourseRegistrationRequest.objects.create(
            student=student,
            course=course,
            status='approved'
        )
        
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert 'already enrolled' in response.data['detail']
    
    def test_rejected_student_can_reregister(self, api_client, student, course):
        """Test that rejected students can register again"""
        # Create rejected registration
        CourseRegistrationRequest.objects.create(
            student=student,
            course=course,
            status='rejected'
        )
        
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_201_CREATED
        # Check that status was updated to pending
        request = CourseRegistrationRequest.objects.get(student=student, course=course)
        assert request.status == 'pending'
    
    def test_invalid_course_code_returns_404(self, api_client, student):
        """Test registration with invalid course code"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': 'INVALID123'})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert 'Invalid course code' in response.data['detail']
    
    def test_educator_cannot_register_as_student(self, api_client, educator, course):
        """Test that educators cannot register as students"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'Only students can register' in response.data['detail']
    
    def test_unauthenticated_user_cannot_register(self, api_client, course):
        """Test that unauthenticated users cannot register"""
        url = reverse('projects:course-register', kwargs={'course_code': course.code})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
    
    def test_case_insensitive_course_code(self, api_client, student, course):
        """Test that course code matching is case insensitive"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code.lower()})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_201_CREATED
        assert CourseRegistrationRequest.objects.filter(
            student=student, 
            course=course
        ).exists()


@pytest.mark.django_db
class TestCourseRegistrationApproveView:
    """Test course registration approval endpoint"""
    
    def test_educator_can_approve_registration(self, api_client, educator, pending_registration):
        """Test that educators can approve registration requests"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': pending_registration.id})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'approved and enrolled' in response.data['detail']
        
        # Check that request was approved
        pending_registration.refresh_from_db()
        assert pending_registration.status == 'approved'
        
        # Check that student was enrolled
        assert pending_registration.course in pending_registration.student.enrolled_courses.all()
    
    def test_non_educator_cannot_approve(self, api_client, student, pending_registration):
        """Test that non-educators cannot approve registrations"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': pending_registration.id})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    def test_wrong_educator_cannot_approve(self, api_client, track, pending_registration):
        """Test that educators can only approve requests for their own courses"""
        # Create different educator
        other_educator = User.objects.create_user(
            username='other_educator',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )
        
        api_client.force_authenticate(user=other_educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': pending_registration.id})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'not the educator for this course' in response.data['detail']
    
    def test_already_approved_request_returns_409(self, api_client, educator, pending_registration):
        """Test that already approved requests return conflict status"""
        # Approve the request first
        pending_registration.status = 'approved'
        pending_registration.save()
        
        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': pending_registration.id})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_409_CONFLICT
        assert 'already approved' in response.data['detail']
    
    def test_nonexistent_request_returns_404(self, api_client, educator):
        """Test that nonexistent registration requests return 404"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': 99999})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_404_NOT_FOUND
        assert 'not found' in response.data['detail']


@pytest.mark.django_db
class TestCourseRegistrationRejectView:
    """Test course registration rejection endpoint"""
    
    def test_educator_can_reject_registration(self, api_client, educator, pending_registration):
        """Test that educators can reject registration requests"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-reject', kwargs={'request_id': pending_registration.id})
        
        response = api_client.post(url)
        
        assert response.status_code == status.HTTP_200_OK
        assert 'rejected' in response.data['detail']
        
        # Check that request was rejected
        pending_registration.refresh_from_db()
        assert pending_registration.status == 'rejected'
    
    def test_non_educator_cannot_reject(self, api_client, student, pending_registration):
        """Test that non-educators cannot reject registrations"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-registration-reject', kwargs={'request_id': pending_registration.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_wrong_educator_cannot_reject(self, api_client, pending_registration):
        """Test that educators can only reject requests for their own courses"""
        # Create different educator
        other_educator = User.objects.create_user(
            username='other_educator_reject',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )

        api_client.force_authenticate(user=other_educator)
        url = reverse('projects:course-registration-reject', kwargs={'request_id': pending_registration.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert 'not the educator for this course' in response.data['detail']

    def test_already_rejected_request_returns_409(self, api_client, educator, pending_registration):
        """Test that already rejected requests return conflict status"""
        # Reject the request first
        pending_registration.status = 'rejected'
        pending_registration.save()

        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-reject', kwargs={'request_id': pending_registration.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_409_CONFLICT
        assert 'already rejected' in response.data['detail']


@pytest.mark.django_db
class TestAllRegistrationRequestsView:
    """Test endpoint for getting all pending registration requests"""

    def test_educator_can_view_pending_requests(self, api_client, educator, track):
        """Test that educators can view all pending requests for their courses"""
        # Create multiple courses and requests
        course1 = Course.objects.create(
            code="COURSE1",
            name="Course 1",
            description="First course",
            track=track,
            educator=educator
        )
        course2 = Course.objects.create(
            code="COURSE2",
            name="Course 2",
            description="Second course",
            track=track,
            educator=educator
        )

        student1 = User.objects.create_user(
            username='student1',
            email='<EMAIL>',
            password='TestPass123',
            user_type='S'
        )
        student2 = User.objects.create_user(
            username='student2',
            email='<EMAIL>',
            password='TestPass123',
            user_type='S'
        )

        # Create pending requests
        req1 = CourseRegistrationRequest.objects.create(
            student=student1,
            course=course1,
            status='pending'
        )
        req2 = CourseRegistrationRequest.objects.create(
            student=student2,
            course=course2,
            status='pending'
        )

        # Create approved request (should not appear)
        CourseRegistrationRequest.objects.create(
            student=student1,
            course=course2,
            status='approved'
        )

        api_client.force_authenticate(user=educator)
        url = reverse('projects:all-registration-requests')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2

        # Check that only pending requests are returned
        request_ids = [req['id'] for req in response.data]
        assert req1.id in request_ids
        assert req2.id in request_ids

    def test_educator_only_sees_own_course_requests(self, api_client, track):
        """Test that educators only see requests for their own courses"""
        educator1 = User.objects.create_user(
            username='educator1',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )
        educator2 = User.objects.create_user(
            username='educator2',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )

        course1 = Course.objects.create(
            code="EDU1COURSE",
            name="Educator 1 Course",
            description="Course by educator 1",
            track=track,
            educator=educator1
        )
        course2 = Course.objects.create(
            code="EDU2COURSE",
            name="Educator 2 Course",
            description="Course by educator 2",
            track=track,
            educator=educator2
        )

        student = User.objects.create_user(
            username='student_multi',
            email='<EMAIL>',
            password='TestPass123',
            user_type='S'
        )

        # Create requests for both courses
        req1 = CourseRegistrationRequest.objects.create(
            student=student,
            course=course1,
            status='pending'
        )
        CourseRegistrationRequest.objects.create(
            student=student,
            course=course2,
            status='pending'
        )

        # Educator 1 should only see their own course request
        api_client.force_authenticate(user=educator1)
        url = reverse('projects:all-registration-requests')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['id'] == req1.id
        assert response.data[0]['course']['code'] == course1.code

    def test_student_cannot_view_requests(self, api_client, student):
        """Test that students cannot view registration requests"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:all-registration-requests')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_403_FORBIDDEN


@pytest.mark.django_db
class TestBulkRegistrationActionView:
    """Test bulk approve/reject endpoint"""

    def test_bulk_approve_requests(self, api_client, educator, track):
        """Test bulk approval of registration requests"""
        course = Course.objects.create(
            code="BULKTEST",
            name="Bulk Test Course",
            description="Course for bulk testing",
            track=track,
            educator=educator
        )

        # Create multiple students and requests
        students = []
        requests = []
        for i in range(3):
            student = User.objects.create_user(
                username=f'bulk_student_{i}',
                email=f'bulk_student_{i}@example.com',
                password='TestPass123',
                user_type='S'
            )
            students.append(student)

            request = CourseRegistrationRequest.objects.create(
                student=student,
                course=course,
                status='pending'
            )
            requests.append(request)

        api_client.force_authenticate(user=educator)
        url = reverse('projects:bulk-registration-action')

        data = {
            'action': 'approve',
            'request_ids': [req.id for req in requests]
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_200_OK

        # Check that all requests were approved
        for request in requests:
            request.refresh_from_db()
            assert request.status == 'approved'

        # Check that all students were enrolled
        for student in students:
            assert course in student.enrolled_courses.all()

    def test_bulk_reject_requests(self, api_client, educator, track):
        """Test bulk rejection of registration requests"""
        course = Course.objects.create(
            code="BULKREJECT",
            name="Bulk Reject Course",
            description="Course for bulk rejection testing",
            track=track,
            educator=educator
        )

        # Create multiple students and requests
        requests = []
        for i in range(2):
            student = User.objects.create_user(
                username=f'reject_student_{i}',
                email=f'reject_student_{i}@example.com',
                password='TestPass123',
                user_type='S'
            )

            request = CourseRegistrationRequest.objects.create(
                student=student,
                course=course,
                status='pending'
            )
            requests.append(request)

        api_client.force_authenticate(user=educator)
        url = reverse('projects:bulk-registration-action')

        data = {
            'action': 'reject',
            'request_ids': [req.id for req in requests]
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_200_OK

        # Check that all requests were rejected
        for request in requests:
            request.refresh_from_db()
            assert request.status == 'rejected'

    def test_bulk_action_wrong_educator(self, api_client, pending_registration):
        """Test that educators can only bulk action their own course requests"""
        other_educator = User.objects.create_user(
            username='other_bulk_educator',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )

        api_client.force_authenticate(user=other_educator)
        url = reverse('projects:bulk-registration-action')

        data = {
            'action': 'approve',
            'request_ids': [pending_registration.id]
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_bulk_action_invalid_action(self, api_client, educator, pending_registration):
        """Test bulk action with invalid action type"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:bulk-registration-action')

        data = {
            'action': 'invalid_action',
            'request_ids': [pending_registration.id]
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_bulk_action_empty_request_ids(self, api_client, educator):
        """Test bulk action with empty request IDs"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:bulk-registration-action')

        data = {
            'action': 'approve',
            'request_ids': []
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.django_db
class TestRegistrationEdgeCases:
    """Test edge cases and error scenarios for registration endpoints"""

    def test_registration_with_special_characters_in_course_code(self, api_client, student, track, educator):
        """Test registration with course codes containing special characters"""
        Course.objects.create(
            code="CS-101_A",
            name="Special Course",
            description="Course with special characters in code",
            track=track,
            educator=educator
        )

        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': 'cs-101_a'})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_201_CREATED

    def test_registration_with_very_long_course_code(self, api_client, student):
        """Test registration with very long course code"""
        api_client.force_authenticate(user=student)
        long_code = 'A' * 100  # Very long course code
        url = reverse('projects:course-register', kwargs={'course_code': long_code})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_concurrent_registration_requests(self, api_client, track, educator):
        """Test handling of concurrent registration requests"""
        course = Course.objects.create(
            code="CONCURRENT",
            name="Concurrent Test Course",
            description="Course for testing concurrent requests",
            track=track,
            educator=educator
        )

        student = User.objects.create_user(
            username='concurrent_student',
            email='<EMAIL>',
            password='TestPass123',
            user_type='S'
        )

        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})

        # Make first request
        response1 = api_client.post(url)
        assert response1.status_code == status.HTTP_201_CREATED

        # Make second request (should be prevented)
        response2 = api_client.post(url)
        assert response2.status_code == status.HTTP_409_CONFLICT

    def test_registration_after_course_deletion(self, api_client, student, course):
        """Test registration attempt after course is deleted"""
        course_code = course.code
        course.delete()

        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course_code})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_approval_after_student_deletion(self, api_client, educator, pending_registration):
        """Test approval attempt after student is deleted"""
        request_id = pending_registration.id
        pending_registration.student.delete()

        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': request_id})

        response = api_client.post(url)

        # Should handle gracefully - either 404 or 500 depending on implementation
        assert response.status_code in [status.HTTP_404_NOT_FOUND, status.HTTP_500_INTERNAL_SERVER_ERROR]

    def test_bulk_action_with_mixed_ownership(self, api_client, track):
        """Test bulk action with requests from different educators"""
        educator1 = User.objects.create_user(
            username='educator_mixed_1',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )
        educator2 = User.objects.create_user(
            username='educator_mixed_2',
            email='<EMAIL>',
            password='TestPass123',
            user_type='E'
        )

        course1 = Course.objects.create(
            code="MIXED1",
            name="Mixed Course 1",
            description="First mixed course",
            track=track,
            educator=educator1
        )
        course2 = Course.objects.create(
            code="MIXED2",
            name="Mixed Course 2",
            description="Second mixed course",
            track=track,
            educator=educator2
        )

        student = User.objects.create_user(
            username='mixed_student',
            email='<EMAIL>',
            password='TestPass123',
            user_type='S'
        )

        req1 = CourseRegistrationRequest.objects.create(
            student=student,
            course=course1,
            status='pending'
        )
        req2 = CourseRegistrationRequest.objects.create(
            student=student,
            course=course2,
            status='pending'
        )

        # Educator 1 tries to approve both requests (should fail for req2)
        api_client.force_authenticate(user=educator1)
        url = reverse('projects:bulk-registration-action')

        data = {
            'action': 'approve',
            'request_ids': [req1.id, req2.id]
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_registration_request_serialization(self, api_client, educator, pending_registration):
        """Test that registration request data is properly serialized"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:all-registration-requests')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1

        request_data = response.data[0]
        required_fields = ['id', 'student', 'course', 'status', 'created_at']

        for field in required_fields:
            assert field in request_data

        assert request_data['status'] == 'pending'
        assert request_data['course']['code'] == pending_registration.course.code

    def test_malformed_bulk_action_data(self, api_client, educator):
        """Test bulk action with malformed request data"""
        api_client.force_authenticate(user=educator)
        url = reverse('projects:bulk-registration-action')

        # Test with missing action
        data = {'request_ids': [1, 2, 3]}
        response = api_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Test with missing request_ids
        data = {'action': 'approve'}
        response = api_client.post(url, data, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Test with non-list request_ids - this might cause 500 due to Django ORM error
        data = {'action': 'approve', 'request_ids': 'not_a_list'}
        response = api_client.post(url, data, format='json')
        assert response.status_code in [status.HTTP_400_BAD_REQUEST, status.HTTP_500_INTERNAL_SERVER_ERROR]


@pytest.mark.django_db
class TestRegistrationPermissions:
    """Test permission and authentication scenarios"""

    def test_unauthenticated_access_to_all_endpoints(self, api_client, course, pending_registration):
        """Test that all registration endpoints require authentication"""
        endpoints = [
            ('projects:course-register', {'course_code': course.code}),
            ('projects:course-registration-approve', {'request_id': pending_registration.id}),
            ('projects:course-registration-reject', {'request_id': pending_registration.id}),
            ('projects:all-registration-requests', {}),
            ('projects:bulk-registration-action', {}),
        ]

        for endpoint_name, kwargs in endpoints:
            url = reverse(endpoint_name, kwargs=kwargs)
            response = api_client.post(url)
            assert response.status_code == status.HTTP_401_UNAUTHORIZED, f"Endpoint {endpoint_name} should require authentication"

    def test_student_access_to_educator_only_endpoints(self, api_client, student, pending_registration):
        """Test that students cannot access educator-only endpoints"""
        api_client.force_authenticate(user=student)

        educator_endpoints = [
            ('projects:course-registration-approve', {'request_id': pending_registration.id}),
            ('projects:course-registration-reject', {'request_id': pending_registration.id}),
            ('projects:all-registration-requests', {}),
            ('projects:bulk-registration-action', {}),
        ]

        for endpoint_name, kwargs in educator_endpoints:
            url = reverse(endpoint_name, kwargs=kwargs)
            response = api_client.post(url)
            assert response.status_code == status.HTTP_403_FORBIDDEN, f"Student should not access {endpoint_name}"

    def test_inactive_user_cannot_register(self, api_client, course):
        """Test that unauthenticated requests cannot register for courses"""
        # Don't authenticate - test actual authentication requirement
        url = reverse('projects:course-register', kwargs={'course_code': course.code})

        response = api_client.post(url)

        # Should require authentication
        assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.django_db
class TestRegistrationDataIntegrity:
    """Test data integrity and consistency"""

    def test_registration_request_timestamps(self, api_client, student, course):
        """Test that registration requests have proper timestamps"""
        api_client.force_authenticate(user=student)
        url = reverse('projects:course-register', kwargs={'course_code': course.code})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_201_CREATED

        request = CourseRegistrationRequest.objects.get(
            student=student,
            course=course
        )

        assert request.created_at is not None
        assert request.updated_at is not None
        assert request.created_at <= request.updated_at

    def test_student_enrollment_consistency(self, api_client, educator, pending_registration):
        """Test that student enrollment is consistent after approval"""
        student = pending_registration.student
        course = pending_registration.course

        # Ensure student is not enrolled initially
        assert course not in student.enrolled_courses.all()

        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': pending_registration.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_200_OK

        # Check both the request status and enrollment
        pending_registration.refresh_from_db()
        student.refresh_from_db()

        assert pending_registration.status == 'approved'
        assert course in student.enrolled_courses.all()

    def test_duplicate_enrollment_prevention(self, api_client, educator, student, course):
        """Test that duplicate enrollments are prevented"""
        # Manually enroll student first
        student.enrolled_courses.add(course)

        # Create and approve a registration request
        request = CourseRegistrationRequest.objects.create(
            student=student,
            course=course,
            status='pending'
        )

        api_client.force_authenticate(user=educator)
        url = reverse('projects:course-registration-approve', kwargs={'request_id': request.id})

        response = api_client.post(url)

        # Should still succeed (idempotent operation)
        assert response.status_code == status.HTTP_200_OK

        # Student should still be enrolled (no duplicates)
        enrollment_count = student.enrolled_courses.filter(code=course.code).count()
        assert enrollment_count == 1
