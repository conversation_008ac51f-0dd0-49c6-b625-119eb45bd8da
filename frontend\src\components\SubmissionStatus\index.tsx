import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Button,
  useTheme,
} from "@mui/material";
import AssignmentTurnedInIcon from "@mui/icons-material/AssignmentTurnedIn";
import api from "../../services/api";
import EvaluatorInfoModal, { EvaluatorInfo } from "../EvaluatorInfoModal";

interface SubmissionStatusProps {
  submissionId: number;
  onStatusChange?: () => void;
}

interface SubmissionStatus {
  status: string;
  evaluations_completed: number;
  evaluations_remaining: number;
  evaluations_required: number;
  final_score: number | null;
  can_resubmit: boolean;
  assigned_evaluator?: EvaluatorInfo;
}

const SubmissionStatus: React.FC<SubmissionStatusProps> = ({
  submissionId,
  onStatusChange,
}) => {
  const [status, setStatus] = useState<SubmissionStatus | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedEvaluator, setSelectedEvaluator] =
    useState<EvaluatorInfo | null>(null);
  const theme = useTheme();

  const fetchStatus = async () => {
    try {
      const response = await api.get(
        `/projects/submissions/${submissionId}/status/`
      );
      setStatus(response.data);

      // If status changed to completed, refresh user data to show new level
      if (
        response.data.status === "completed" &&
        status?.status !== "completed"
      ) {
        onStatusChange?.();
      }
    } catch (error) {
      console.error("Error fetching submission status:", error);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, [submissionId]);

  if (!status) return null;

  const getStatusColor = () => {
    switch (status.status) {
      case "completed":
        return theme.palette.success.main;
      case "failed":
        return theme.palette.error.main;
      case "in_evaluation":
        return theme.palette.warning.main;
      default:
        return theme.palette.info.main;
    }
  };

  const getStatusText = () => {
    switch (status.status) {
      case "completed":
        return "Completed";
      case "failed":
        return "Failed";
      case "in_evaluation":
        return "In Evaluation";
      default:
        return "Pending";
    }
  };

  const progress =
    (status.evaluations_completed / status.evaluations_required) * 100;

  return (
    <Card
      sx={{
        background:
          "linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(123, 137, 244, 0.1))",
        border: "1px solid rgba(100, 255, 218, 0.2)",
        mb: 2,
      }}
    >
      <CardContent>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <AssignmentTurnedInIcon sx={{ mr: 1, color: getStatusColor() }} />
          <Typography variant="h6" component="div">
            Evaluation Progress
          </Typography>
          {status.assigned_evaluator && (
              <Chip
                label={`Evaluator: ${status.assigned_evaluator.first_name} ${status.assigned_evaluator.last_name}`}
                size="small"
                sx={{ ml: 2, cursor: "pointer" }}
                color="primary"
                variant="outlined"
                onClick={() => {
                  if (status.assigned_evaluator) {
                    setSelectedEvaluator(status.assigned_evaluator);
                    setModalOpen(true);
                  }
                }}
              />
          )}
          <Chip
            label={getStatusText()}
            size="small"
            sx={{
              ml: "auto",
              bgcolor: `${getStatusColor()}20`,
              color: getStatusColor(),
              borderColor: getStatusColor(),
            }}
            variant="outlined"
          />
        </Box>

        <Box sx={{ width: "100%", mb: 2 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 10,
              borderRadius: 5,
              bgcolor: theme.palette.grey[200],
              "& .MuiLinearProgress-bar": {
                bgcolor: getStatusColor(),
              },
            }}
          />
          <Typography
            variant="body2"
            color="text.secondary"
            align="right"
            sx={{ mt: 0.5 }}
          >
            {status.evaluations_completed} of {status.evaluations_required}{" "}
            evaluations completed
          </Typography>
        </Box>

        {status.final_score !== null && (
          <Typography variant="body1" sx={{ mb: 1 }}>
            Final Score: {status.final_score}%
          </Typography>
        )}

        {status.can_resubmit && (
          <Button
            variant="contained"
            color="primary"
            fullWidth
            onClick={onStatusChange}
            sx={{ mt: 1 }}
          >
            Resubmit Project
          </Button>
        )}
      </CardContent>
      <EvaluatorInfoModal
        open={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setSelectedEvaluator(null);
        }}
        evaluator={selectedEvaluator}
      />
    </Card>
  );
};

export default SubmissionStatus;
