from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
import os
import uuid
from core.storages import generate_filename

def get_profile_picture_path(instance, filename):
    ext = filename.split('.')[-1]
    # Use test filename generator
    filename = generate_filename(filename)
    return os.path.join('profile_pictures', filename)

def generate_approval_code():
    return str(uuid.uuid4())

class Track(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return self.name

class UserManager(BaseUserManager):
    def create_user(self, username, email, password=None, **extra_fields):
        if not username:
            raise ValueError('The Username field must be set')
        if not email:
            raise ValueError('The Email field must be set')
        if not password:
            raise ValueError('The Password field must be set')
    
        email = self.normalize_email(email)
        user = self.model(username=username, email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, username, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(username, email, password, **extra_fields)

class User(AbstractUser):
    username = models.CharField(max_length=50, unique=True, null=False)
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=100)
    last_name = models.CharField(max_length=100)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    is_approved = models.BooleanField(default=False)  # For admin approval
    is_email_verified = models.BooleanField(default=False)  # For email verification
    approval_code = models.CharField(max_length=100, blank=True, null=True, unique=True)
    admin_approval_code = models.CharField(max_length=100, blank=True, null=True, unique=True)
    profile_picture = models.ImageField(upload_to=get_profile_picture_path, null=True, blank=True)
    points = models.IntegerField(default=2)
    exp = models.IntegerField(default=0)
    level = models.IntegerField(default=1)
    USER_TYPES = [
        ('S', 'Student'),
        ('E', 'Educator'),
    ]
    user_type = models.CharField(choices=USER_TYPES, null=False, blank=False)
    enrolled_courses = models.ManyToManyField("projects.Course", related_name='enrolled_students', blank=True)
    current_course = models.ForeignKey("projects.Course", on_delete=models.SET_NULL, null=True, blank=True, related_name='current_students')

    objects = UserManager()

    def __str__(self):
        return self.username

    def save(self, *args, **kwargs):
        # Auto-approve students upon registration
        if not self.pk and self.user_type == 'S':  # New student user
            self.is_approved = True

        # Generate admin approval code for educators
        if self.user_type == 'E' and not self.admin_approval_code:
            self.admin_approval_code = generate_approval_code()

        # Ensure level doesn't decrease
        if self.pk:
            try:
                old_instance = User.objects.get(pk=self.pk)
                if self.level < old_instance.level:
                    self.level = old_instance.level
            except User.DoesNotExist:
                pass
        super().save(*args, **kwargs)

    def increase_level(self):
        """Safely increase user level by 1. Level can only increase, never decrease."""
        self.level += 1
        self.save(update_fields=['level'])