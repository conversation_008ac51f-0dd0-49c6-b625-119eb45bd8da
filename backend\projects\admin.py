from django.contrib import admin
from .models import Project, ProjectSubmission, Evaluation , Question, Course
from django.utils.html import format_html


class QuestionInline(admin.TabularInline):
    model = Question
    extra = 1

class EvaluationInline(admin.TabularInline):
    model = Evaluation
    extra = 0
    readonly_fields = ('evaluator', 'comments', 'score', 'is_approved', 'created_at')
    fields = ('evaluator', 'score', 'is_approved', 'comments', 'created_at')
    can_delete = False
    
    def has_add_permission(self, request, obj=None):
        return False  # Prevent adding evaluations through the inline

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('title', 'course', 'id', 'points_required', 'required_evaluators', 'created_at')
    list_filter = ('required_evaluators', 'course', 'course__track')
    search_fields = ('title', 'description', 'course__name')
    ordering = ('course', 'points_required')
    inlines = [QuestionInline]
    fieldsets = (
        (None, {
            'fields': ('title', 'description', 'pdf_file', 'course')
        }),
        ('Requirements', {
            'fields': ('points_required', 'prerequisite', 'passing_score', 'required_evaluators')
        })
    )
    readonly_fields = ('course',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('course', 'course__track', 'prerequisite')

@admin.register(ProjectSubmission)
class ProjectSubmissionAdmin(admin.ModelAdmin):
    list_display = ('project', 'get_course', 'submitted_by', 'status', 'evaluation_progress', 'submission_link', 'created_at')
    list_filter = ('status', 'submission_type', 'project__course', 'project__course__track')
    search_fields = ('project__title', 'project__course__name', 'submitted_by__username', 'github_repo')
    raw_id_fields = ('submitted_by', 'project')
    fields = ('project', 'submitted_by', 'status', 'submission_type', 'github_repo', 'zip_file', 'final_score')
    readonly_fields = ('submitted_by', 'submission_type', 'github_repo', 'zip_file')
    inlines = [EvaluationInline]
    
    def get_course(self, obj):
        return obj.project.course.name if obj.project.course else '-'
    get_course.short_description = 'Course'
    get_course.admin_order_field = 'project__course__name'
    
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        return form

    def evaluation_progress(self, obj):
        evaluation_count = obj.evaluations.count() if hasattr(obj, 'evaluations') else 0
        required = obj.project.required_evaluators if hasattr(obj.project, 'required_evaluators') else 0
        return f'{evaluation_count}/{required} evaluations'
    evaluation_progress.short_description = 'Evaluation Progress'

    def submission_link(self, obj):
        if obj.submission_type == 'github' and obj.github_repo:
            return format_html('<a href="{}" target="_blank">View Project URL</a>', obj.github_repo)
        elif obj.submission_type == 'zip' and obj.zip_file:
            return format_html('<a href="{}" target="_blank">Download ZIP</a>', obj.zip_file.url)
        return '-'
    submission_link.short_description = 'Project Submission'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('project', 'project__course', 'submitted_by')

@admin.register(Evaluation)
class EvaluationAdmin(admin.ModelAdmin):
    list_display = ('get_project', 'get_submitter', 'evaluator', 'is_approved', 'created_at')
    list_filter = ('is_approved', 'submission__project__course')
    search_fields = ('submission__project__title', 'evaluator__username', 'submission__submitted_by__username')
    raw_id_fields = ('evaluator', 'submission')

    def get_project(self, obj):
        return obj.submission.project.title
    get_project.short_description = 'Project'
    get_project.admin_order_field = 'submission__project__title'

    def get_submitter(self, obj):
        return obj.submission.submitted_by.username
    get_submitter.short_description = 'Submitter'
    get_submitter.admin_order_field = 'submission__submitted_by__username'

    def get_queryset(self, request):
        # Optimize queries
        return super().get_queryset(request).select_related(
            'submission', 'submission__project', 'submission__submitted_by', 'evaluator'
        )

@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ('text', 'project', 'get_course', 'weight', 'response', 'created_at')
    search_fields = ('text', 'project__title', 'project__course__name')
    list_filter = ('response', 'project__course')
    
    def get_course(self, obj):
        return obj.project.course.name if obj.project.course else '-'
    get_course.short_description = 'Course'
    get_course.admin_order_field = 'project__course__name'

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'educator', 'track', 'project_count', 'created_at')
    search_fields = ('name', 'description', 'code', 'educator__username')
    list_filter = ('track', 'educator', 'created_at')
    readonly_fields = ('code',)
    
    def project_count(self, obj):
        return obj.projects.count()
    project_count.short_description = 'Number of Projects'
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('educator', 'track').prefetch_related('projects')