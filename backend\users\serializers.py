from rest_framework import serializers
from .models import User, Track
from .utils import validate_password
from django.contrib.auth import authenticate


class TrackSerializer(serializers.ModelSerializer):
    class Meta:
        model = Track
        fields = ['id', 'name', 'description', 'created_at']
        read_only_fields = ['created_at']

class UserSerializer(serializers.ModelSerializer):
    enrolled_courses = serializers.SerializerMethodField()
    current_course = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('id', 'username', 'email', 'first_name', 'last_name',
                 'points', 'exp', 'level', 'user_type', 'profile_picture',
                 'is_approved', 'is_email_verified', 'phone_number', 'enrolled_courses', 'current_course')
        read_only_fields = (
            'email',
            'user_type',
            'points',           # Should be system-managed (through Evaluations and Submissions)
            'level',            # Should be system-managed (through Evaluations and Submissions)
            'exp',              # Should be system-managed (through Evaluations and Submissions)
            'is_approved',      # Should only be modified through admin approval process
            'is_email_verified', # Should only be modified through email verification process
            'enrolled_courses',
        )

    def validate(self, attrs):
        request = self.context.get('request')
        user = self.context.get('user') or self.instance

        # Handle profile picture validation
        if request and request.FILES.get('profile_picture'):
            file = request.FILES['profile_picture']
            if file.size > 2 * 1024 * 1024:
                raise serializers.ValidationError({
                    'profile_picture': 'The file is too large. Please ensure your image is under 2MB.'
                })
        elif 'profile_picture' in attrs:
            # If profile_picture is empty string or null, clear it
            if not attrs['profile_picture']:
                # Delete the old file if it exists
                if user.profile_picture:
                    user.profile_picture.delete(save=False)
                attrs['profile_picture'] = None

        # Handle username validation
        if 'username' in attrs:
            new_username = attrs['username']
            if new_username == user.username:
                raise serializers.ValidationError({
                    'username': 'Username cannot be the same as the current username'
                })
            if User.objects.filter(username=new_username).exclude(id=user.id).exists():
                raise serializers.ValidationError({
                    'username': 'Username already taken'
                })

        # Handle current_course validation
        if 'current_course' in attrs:
            course_code = attrs['current_course']
            if course_code:
                # Import here to avoid circular imports
                from projects.models import Course
                try:
                    course = Course.objects.get(code=course_code)
                    # Check if user is enrolled in this course
                    if course not in user.enrolled_courses.all():
                        raise serializers.ValidationError({
                            'current_course': 'You are not enrolled in this course'
                        })
                    attrs['current_course'] = course
                except Course.DoesNotExist:
                    raise serializers.ValidationError({
                        'current_course': 'Course not found'
                    })
            else:
                attrs['current_course'] = None

        return attrs

    def to_representation(self, instance):
        data = super().to_representation(instance)
        request = self.context.get('request')
        if request and instance.profile_picture:
            data['profile_picture'] = request.build_absolute_uri(instance.profile_picture.url)

        return data

    def get_enrolled_courses(self, obj):
        # Only return data if user is a STUDENT, not an educator!
        if getattr(obj, 'user_type', None) == 'S':
            # DO NOT REMOVE IMPORT FROM HERE, this import is necessary to avoid circular import issues
            from projects.serializers import CourseSerializer
            return CourseSerializer(obj.enrolled_courses.all(), many=True, context=self.context).data
        return None

    def get_current_course(self, obj):
        # Return current course data if it exists
        if obj.current_course:
            # DO NOT REMOVE IMPORT FROM HERE, this import is necessary to avoid circular import issues
            from projects.serializers import CourseSerializer
            return CourseSerializer(obj.current_course, context=self.context).data
        return None

class UserRegistrationSerializer(serializers.ModelSerializer):
    password_confirm = serializers.CharField(write_only=True, required=True)
    
    class Meta:
        model = User
        fields = ['email', 'first_name', 'last_name', 'user_type', 'password', 'password_confirm', 'phone_number']
        extra_kwargs = {
            'password': {'write_only': True},
            'first_name': {'required': True},
            'last_name': {'required': True},
            'email': {'required': True},
            'user_type': {'required': True},
        }

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError({"password": "Password fields didn't match."})
        
        is_valid, error_message = validate_password(attrs['password'], {
            'email': attrs.get('email'),
            'first_name': attrs.get('first_name'),
            'last_name': attrs.get('last_name')
        })
        if not is_valid:
            raise serializers.ValidationError({"password": error_message})

        # Validate email format
        email = attrs['email']
        if User.objects.filter(email=email).exists():
            raise serializers.ValidationError({"email": "This email is already registered."})
            
        return attrs

    def create(self, validated_data):
        validated_data.pop('password_confirm', None)

        # Generate username from email
        email = validated_data['email']
        username = email.split('@')[0]

        # If username already exists, append numbers until we find a unique one
        base_username = username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{base_username}{counter}"
            counter += 1

        # Create user with generated username, track and zone
        user = User.objects.create_user(
            username=username,
            is_approved=False,
            **validated_data
        )
        return user

class UserLoginSerializer(serializers.Serializer):
    email = serializers.EmailField()
    password = serializers.CharField()

    def validate(self, data):
        try:
            user = User.objects.get(email=data['email'])
        except User.DoesNotExist:
            raise serializers.ValidationError("Invalid credentials")

        if user and user.check_password(data['password']):
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled")
            return user
            
        raise serializers.ValidationError("Invalid credentials")
