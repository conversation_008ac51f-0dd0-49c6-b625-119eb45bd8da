import React from 'react';
import { <PERSON>, Container, <PERSON><PERSON><PERSON>, Button, Grid2 as Grid, Paper, Accordion, AccordionSummary, AccordionDetails } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CodeIcon from '@mui/icons-material/Code';
import GroupsIcon from '@mui/icons-material/Groups';
import SchoolIcon from '@mui/icons-material/School';
import { useNavigate } from 'react-router-dom';
import StarField from '@/components/StarField';
import LandingHeader from '@/components/LandingHeader';
import SectionTitle from '@/components/SectionTitle';
import Footer from '@/components/Footer';

const Landing: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <CodeIcon sx={{ fontSize: 40 }} />,
      title: "Project-Based Learning",
      description: "Learn by building real projects. Apply your knowledge immediately with hands-on experience."
    },
    {
      icon: <GroupsIcon sx={{ fontSize: 40 }} />,
      title: "Peer Evaluation System",
      description: "Get feedback from peers and learn to evaluate others' code. Grow together as developers."
    },
    {
      icon: <SchoolIcon sx={{ fontSize: 40 }} />,
      title: "Track-Based Curriculum",
      description: "Choose your learning path. Follow a structured curriculum designed for your success."
    }
  ];

  const tracks = [
    {
      name: "Frontend Development",
      description: "Master modern web development through hands-on projects. Build responsive websites, interactive applications, and learn essential frameworks like React. Get real feedback from peers and improve your code through collaborative reviews.",
      projects: "4 projects",
    },
    {
      name: "Backend Development",
      description: "Build robust server-side applications and APIs. Learn database design, authentication, and API development through practical projects. Collaborate with peers to understand different approaches to solving backend challenges.",
      projects: "4 projects",
    },
    
  ];

  const faqs = [
    {
      question: "How does the peer evaluation system work?",
      answer: "After completing a project, you'll review others' code and receive feedback on yours. This helps build code review skills and deepens understanding."
    },
    {
      question: "Do I need prior programming experience?",
      answer: "No! Our tracks start from the basics and gradually progress to advanced topics. Choose the track that matches your current level."
    },
    {
      question: "How long does it take to complete a track?",
      answer: "It varies by track and your dedication. Most students complete a track in 3-6 months while studying part-time."
    }
  ];

  return (
    <Box sx={{ minHeight: '100vh', position: 'relative' }}>
      <StarField />
      <LandingHeader />
      
      {/* Hero Section */}
      <Container sx={{ pt: 20, pb: 15, position: 'relative', textAlign: 'center' }}>
        <Typography 
          variant="h2" 
          sx={{ 
            fontWeight: 700,
            mb: 2,
            background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          Learn Programming Through Projects
        </Typography>
        <Typography variant="h5" sx={{ mb: 4, color: 'text.secondary' }}>
          Master software development with hands-on projects and peer learning
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3 }}>
          <Button 
            variant="contained" 
            size="large"
            onClick={() => navigate('/register', { state: { role: 'teacher' } })}
            sx={{
              background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
              '&:hover': {
                background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
              },
              px: 4,
              py: 1.5,
            }}
          >
            Teacher?
          </Button>
          <Button 
            variant="contained" 
            size="large"
            onClick={() => navigate('/register', { state: { role: 'student' } })}
            sx={{
              background: 'linear-gradient(45deg, #7B89F4, #64FFDA)',
              '&:hover': {
                background: 'linear-gradient(45deg, #A5B4FF, #5A6AD4)',
              },
              px: 4,
              py: 1.5,
            }}
          >
            Student?
          </Button>
        </Box>
      </Container>

      {/* Features Section */}
      <Box id="features" sx={{ py: 10, bgcolor: 'rgba(10, 25, 47, 0.7)' }}>
        <Container>
          <SectionTitle title="How It Works" />
          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid size={{ xs: 12, md: 4 }} key={index}>
                <Paper 
                  elevation={3}
                  sx={{ 
                    p: 4, 
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    textAlign: 'center',
                    bgcolor: 'rgba(100, 255, 218, 0.05)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(100, 255, 218, 0.1)',
                  }}
                >
                  {feature.icon}
                  <Typography variant="h5" sx={{ my: 2 }}>
                    {feature.title}
                  </Typography>
                  <Typography color="text.secondary">
                    {feature.description}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Tracks Section */}
      <Box id="tracks" sx={{ py: 10 }}>
        <Container>
          <SectionTitle title="Learning Tracks" />
          <Grid 
            container 
            spacing={4} 
            justifyContent="center"
            alignItems="stretch"
          >
            {tracks.map((track, index) => (
              <Grid size={{ xs: 12, md: 4 }} key={index}>
                <Paper 
                  elevation={3}
                  sx={{ 
                    p: 4,
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    bgcolor: 'rgba(100, 255, 218, 0.05)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(100, 255, 218, 0.1)',
                  }}
                >
                  <Typography variant="h5" sx={{ mb: 2 }}>
                    {track.name}
                  </Typography>
                  <Typography color="text.secondary" sx={{ mb: 2, flex: 1 }}>
                    {track.description}
                  </Typography>
                  <Box>
                    <Typography color="primary" sx={{ mb: 1 }}>
                      {track.projects}
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* FAQ Section */}
      <Box id="faq" sx={{ py: 10, bgcolor: 'rgba(10, 25, 47, 0.7)' }}>
        <Container>
          <SectionTitle title="FAQ" />
          {faqs.map((faq, index) => (
            <Accordion 
              key={index}
              sx={{ 
                bgcolor: 'rgba(100, 255, 218, 0.05)',
                backdropFilter: 'blur(10px)',
                '&:before': { display: 'none' },
                mb: 2,
              }}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">{faq.question}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Typography color="text.secondary">
                  {faq.answer}
                </Typography>
              </AccordionDetails>
            </Accordion>
          ))}
        </Container>
      </Box>

      <Footer />
    </Box>
  );
};

export default Landing;