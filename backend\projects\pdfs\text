
    backend

number: 01
title - Welcome to Your Backend Adventure!
Description:
Get ready to embark on an exciting journey into the world of backend development! We’ll
start with an HTML and CSS project that will help you understand the importance of backend
functionality. Through this project, you’ll discover how the backend enhances your web
applications and provides essential features like data storage, user authentication, and
dynamic content generation. Let’s dive in and unlock the power of backend development
together!


number: 02
title - Python for Backend Development
Description:
This track introduces fundamental Python programming skills needed to
build backend applications. Each section includes topics, practical
exercises, projects, and expected outcomes. By completing this track,
you'll be prepared to transition into using fastapi for web development

number: 03
title - Getting Started with FastAPI Projects
Description:
This level teaches practical FastAPI development through hands-on projects.
You'll build two complete APIs - a Book Collection and Recipe Box - learning
FastAPI's core features while solving real-world problems. By the end, you'll
understand how to create, structure, and test FastAPI applications, preparing
you for more advanced concepts like authentication and databases in Level 3.

number: 04
title - Intermediate – Working with API’S , Data and Authentication
Description:
Welcome to Level 3! This level is focused on helping you build a strong backend foundation
with FastAPI. By the end of this level, you’ll be comfortable working with data, building
routes, and implementing authentication to secure your APIs.
You’ll apply everything you learn in a Project-Based Approach.
The main project will be a To-Do List API—a practical backend application that lets
manage tasks.




________________________________________________________________________________________________


Frontend 


number: 01
title - HTML & CSS Foundations
Description:
To establish a solid understanding of the foundational elements of web development
by learning HTML (Hypertext Markup Language) for structuring web content and
CSS (Cascading Style Sheets) for styling that content.


number: 02
title - JavaScript Learning Path: Building a Task Manager
Description:
Learn JavaScript by building a practical Task Manager application. This
project-based approach will teach you core JavaScript concepts while creating
something useful. You'll start with basic functionality in the console and gradually
build up to a full web application with a user interface.

number: 03
title - Intermediate – Building a Strong Frontend Foundation
Description:
Welcome to Level 03! In this level, you will strengthen your frontend development skills by
learning to combine HTML, CSS, and JavaScript to build dynamic and interactive web
pages.
You’ll work on a Project-Based Approach. The main project will be a To-Do List Web App,
where you’ll focus on creating, styling, and adding interactivity to a user-friendly interface.
(remember the image design it's always just an example its not mandatory to do the same)

number: 04
title - Intermediate – React Fundamentals Through Projects
Description:
In this project, you will build a Weather Dashboard that allows users to search for a city and
view weather information (like temperature, humidity, and conditions). You’ll integrate an API
to fetch real-time weather data and display it dynamically.