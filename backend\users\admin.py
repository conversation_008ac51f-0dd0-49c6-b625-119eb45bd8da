# Standard library imports
import logging
import os
import re
from datetime import timedelta

# Third-party imports
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

# Django imports
from django.contrib import admin, messages
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.hashers import make_password
from django.db.models import Count, Avg, Q, Sum, Max
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from django.urls import path, reverse
from django.utils import timezone
from django.utils.html import format_html

# Local imports
from .email_utils import send_brevo_email
from .models import User, Track
logger = logging.getLogger(__name__)

def style_worksheet(ws):
    header_fill = PatternFill(start_color='B3E0FF', end_color='B3E0FF', fill_type='solid')
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.rows:
        for cell in row:
            cell.border = border
            if cell.row == 1:
                cell.font = Font(bold=True)
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center')

    # Adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                max_length = max(max_length, len(str(cell.value)))
            except:
                pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

# def generate_zone_report(zone):
#     wb = Workbook()
#     ws = wb.active
#     ws.title = "User Analytics"

#     headers = [
#         "Username",
#         "First Name",
#         "Last Name",
#         "Email",
#         "Level",
#         "Track",
#         "Total Submissions",
#         "Completed Projects",
#         "Success Rate",
#         "Average Score",
#         "Evaluations Given",
#         "Last Active",
#         "Activity Score"
#     ]
#     ws.append(headers)

#     users = User.objects.filter(zone=zone).select_related('track')
#     now = timezone.now()

#     for user in users:
#         submissions = user.submitted_projects.all()
#         completed_submissions = submissions.filter(status='completed')
#         evaluations = user.evaluations.all()
        
#         total_submissions = submissions.count()
#         completed_count = completed_submissions.count()
#         success_rate = (completed_count / total_submissions * 100) if total_submissions > 0 else 0
#         avg_score = completed_submissions.aggregate(Avg('final_score'))['final_score__avg'] or 0
        
#         activity_score = (
#             completed_count * 10 +
#             evaluations.count() * 5 +
#             user.level * 3
#         )

#         row = [
#             user.username,
#             user.first_name,
#             user.last_name,
#             user.email,
#             user.level,
#             user.track.name if user.track else "No Track",
#             total_submissions,
#             completed_count,
#             f"{success_rate:.1f}%",
#             f"{avg_score:.1f}%",
#             evaluations.count(),
#             user.last_login.strftime('%Y-%m-%d') if user.last_login else "Never",
#             activity_score
#         ]
#         ws.append(row)

#     style_worksheet(ws)

#     # Add summary sheet
#     summary_ws = wb.create_sheet("Zone Summary")
#     total_users = users.count()
#     active_users = users.filter(last_login__gte=now - timedelta(days=30)).count()
    
#     # Calculate zone-wide statistics
#     total_submissions = sum(user.submitted_projects.count() for user in users)
#     total_completed = sum(user.submitted_projects.filter(status='completed').count() for user in users)
#     total_evaluations = sum(user.evaluations.count() for user in users)
#     avg_success_rate = (total_completed / total_submissions * 100) if total_submissions > 0 else 0
    
#     summary_data = [
#         ["Zone Analytics Summary", ""],
#         ["Metric", "Value"],
#         ["Total Users", total_users],
#         ["Active Users (30 days)", active_users],
#         ["Average User Level", f"{users.aggregate(Avg('level'))['level__avg']:.1f}"],
#         ["Total Projects Submitted", total_submissions],
#         ["Total Projects Completed", total_completed],
#         ["Total Evaluations Given", total_evaluations],
#         ["Average Success Rate", f"{avg_success_rate:.1f}%"],
#     ]
    
#     for row in summary_data:
#         summary_ws.append(row)
    
#     style_worksheet(summary_ws)
    
#     return wb

# @admin.register(Zone)
# class ZoneAdmin(admin.ModelAdmin):
#     list_display = ('name', 'id', 'description', 'created_at')
#     search_fields = ('name', 'description')
#     ordering = ('name',)
#     readonly_fields = ('created_at',)
#     fields = ('name', 'description', 'created_at')
#     list_per_page = 20

#     actions = ['generate_zone_report']

#     def generate_zone_report(self, request, queryset):
#         if len(queryset) != 1:
#             self.message_user(request, "Please select exactly one zone to generate a report.", level=messages.WARNING)
#             return

#         zone = queryset[0]
#         wb = generate_zone_report(zone)
        
#         # Create the HTTP response with the Excel file
#         response = HttpResponse(
#             content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
#         )
#         response['Content-Disposition'] = f'attachment; filename=zone_{zone.name}_analytics_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        
#         wb.save(response)
#         return response
    
#     generate_zone_report.short_description = "Generate analytics report for selected zone"

@admin.register(Track)
class TrackAdmin(admin.ModelAdmin):
    list_display = ('name', 'id', 'description', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at',)
    fields = ('name', 'description', 'created_at')
    list_per_page = 20

class CustomUserAdmin(UserAdmin):
    list_display = (
        'username', 'id', 'email', 'first_name', 'last_name', 'phone_number',
        'user_type', 'points', 'level', 'exp', 'is_approved', 'is_email_verified', 'date_joined'
    )
    list_filter = (
        'is_approved', 'is_email_verified', 'first_name', 'last_name',
        'user_type', 'is_staff', 'is_active'
    )
    search_fields = ('username', 'email')
    ordering = ('-date_joined',)
    change_list_template = "admin/users/user/change_list.html"

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': (
            'first_name', 'last_name', 'email', 'phone_number', 'profile_picture'
        )}),
        ('Permissions', {'fields': (
            'is_active', 'is_staff', 'is_superuser', 'is_approved', 'is_email_verified',
            'groups', 'user_permissions'
        )}),
        ('Codes', {'fields': ('approval_code', 'admin_approval_code')}),
        ('Progress', {'fields': ('points', 'exp', 'level')}),
        ('User Type', {'fields': ('user_type',)}),
        ('Courses', {'fields': ('enrolled_courses',)}),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'username', 'email', 'first_name', 'last_name', 'phone_number',
                'profile_picture', 'password1', 'password2',
                'points', 'exp', 'level', 'user_type', 'enrolled_courses',
                'approval_code', 'admin_approval_code',
                'is_approved', 'is_email_verified', 'groups', 'user_permissions'
            ),
        }),
    )

    readonly_fields = ('last_login',)
    filter_horizontal = ('enrolled_courses',)

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('upload-excel/', self.upload_excel_view, name='user-upload-excel'),
            path('send-bulk-email/', self.send_bulk_email_view, name='user-send-bulk-email'),
        ]
        return custom_urls + urls

    def upload_excel_view(self, request):
        if request.method == 'POST':
            # Check user permissions
            if not request.user.is_superuser:
                messages.error(request, 'Only administrators can upload Excel files')
                return HttpResponseRedirect(request.path_info)

            excel_file = request.FILES.get('excel_file')
            # zone_id = request.POST.get('zone')
            track_id = request.POST.get('track')

            if not all([excel_file, track_id]):
                messages.error(request, 'Please provide all required fields')
                return HttpResponseRedirect(request.path_info)

            try:
                # zone = Zone.objects.get(id=zone_id)
                track = Track.objects.get(id=track_id)
            except (Track.DoesNotExist):
                messages.error(request, 'Invalid track selected')
                return HttpResponseRedirect(request.path_info)

            try:
                # Read Excel file
                workbook = openpyxl.load_workbook(excel_file)
                worksheet = workbook.active
                
                # Convert worksheet to list of lists
                data = [[cell.value for cell in row] for row in worksheet.rows]
                if not data:
                    raise ValueError("The Excel file is empty")
                
                # Validate headers
                headers = [str(col).lower().strip() for col in data[0]]
                required_columns = ['email', 'first_name', 'last_name']
                missing_columns = [col for col in required_columns if col not in headers]
                if missing_columns:
                    raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

                # Get column indices
                col_indices = {col: headers.index(col) for col in required_columns}
                phone_index = headers.index('phone_number') if 'phone_number' in headers else None
                
                # Process each row
                created_count = 0
                for row in data[1:]:  # Skip header row
                    email = str(row[col_indices['email']]).strip()
                    if not email or '@' not in email:
                        continue

                    # Get phone number if it exists
                    phone = ''
                    if phone_index is not None and len(row) > phone_index:
                        phone = str(row[phone_index]).strip() if row[phone_index] else ''
                        if phone:
                            if len(phone) == 9 and phone.startswith('50'):
                                phone = '0' + phone
                            if not re.match(r'^050\d{7}$', phone):
                                phone = ''  # Invalid phone number format, set to empty

                    username = email.split('@')[0]
                    
                    # If username already exists, append numbers until we find a unique one
                    base_username = username
                    counter = 1
                    while User.objects.filter(username=username).exists():
                        username = f"{base_username}{counter}"
                        counter += 1

                    # Create user with generated username
                    user = User.objects.create(
                        username=username,
                        email=email,
                        first_name=str(row[col_indices['first_name']]).strip(),
                        last_name=str(row[col_indices['last_name']]).strip(),
                        phone_number=phone,
                        # zone=zone,
                        track=track,
                        password=make_password('42Devspace@ATA'),
                        is_active=True,
                        is_approved=True,
                        is_email_verified=True,
                    )
                    created_count += 1

                messages.success(request, f'Successfully created {created_count} new users')
                return HttpResponseRedirect(reverse('admin:users_user_changelist'))

            except Exception as e:
                messages.error(request, f'Error processing file: {str(e)}')
                return HttpResponseRedirect(request.path_info)

        context = {
            'title': 'Upload Excel File',
            # 'zones': Zone.objects.all(),
            'tracks': Track.objects.all(),
        }
        return render(request, 'admin/users/user/upload_excel.html', context)

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return self.readonly_fields
        return ()

    def approve_users(self, request, queryset):
        queryset.update(is_approved=True)
    approve_users.short_description = 'Approve selected users'

    def approve_emails(self, request, queryset):
        queryset.update(is_email_verified=True)
    approve_emails.short_description = 'Approve selected users emails'

    def unapprove_users(self, request, queryset):
        queryset.update(is_approved=False)
    unapprove_users.short_description = 'Unapprove selected users'

    def unapprove_emails(self, request, queryset):
        queryset.update(is_email_verified=False)
    unapprove_emails.short_description = 'Unapprove selected users emails'

    actions = [approve_users, approve_emails, unapprove_users, unapprove_emails, 'send_bulk_email']

    def send_bulk_email_view(self, request):
        if request.method == 'POST':
            if not request.user.is_superuser:
                self.message_user(request, 'Only administrators can send bulk emails', level=messages.ERROR)
                return HttpResponseRedirect('.')
            
            if 'excel_file' not in request.FILES:
                self.message_user(request, 'Please upload an Excel file', level=messages.ERROR)
                return HttpResponseRedirect('.')

            excel_file = request.FILES['excel_file']
            subject = request.POST.get('subject')
            html_template = request.POST.get('html_content')

            if not all([subject, html_template]):
                self.message_user(request, 'Please provide both subject and email content', level=messages.ERROR)
                return HttpResponseRedirect('.')

            try:
                wb = openpyxl.load_workbook(excel_file, data_only=True)
                ws = wb.active
                headers = [cell.value.lower() if cell.value else '' for cell in ws[1]]
                
                required_columns = ['email', 'first_name', 'last_name']
                missing_columns = [col for col in required_columns if col not in headers]
                
                if missing_columns:
                    self.message_user(request, f'Excel file must contain these columns: {", ".join(missing_columns)}', level=messages.ERROR)
                    return HttpResponseRedirect('.')

                email_col = headers.index('email')
                first_name_col = headers.index('first_name')
                last_name_col = headers.index('last_name')
                
                email_data = []
                errors = []

                for row in ws.iter_rows(min_row=2):
                    email = row[email_col].value
                    first_name = row[first_name_col].value
                    last_name = row[last_name_col].value
                    
                    if not all([email, first_name, last_name]):
                        continue
                        
                    if not isinstance(email, str):
                        continue

                    # Basic email validation
                    if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                        # Format the HTML content with recipient's data
                        try:
                            formatted_content = html_template.format(
                                first_name=first_name,
                                last_name=last_name,
                                email=email
                            )
                            email_data.append({
                                'email': email,
                                'body': formatted_content
                            })
                        except KeyError as e:
                            errors.append(f'Template error with email {email}: Invalid placeholder {str(e)}')
                        except Exception as e:
                            errors.append(f'Template error with email {email}: {str(e)}')
                    else:
                        errors.append(f'Invalid email format: {email}')

                if errors:
                    self.message_user(request, f'Found {len(errors)} errors: {", ".join(errors[:5])}{"..." if len(errors) > 5 else ""}', level=messages.WARNING)

                if email_data:
                    success_count = 0
                    error_count = 0
                    
                    for data in email_data:
                        try:
                            send_brevo_email(
                                to_emails=data['email'],
                                subject=subject,
                                html_content=data['body']
                            )
                            success_count += 1
                        except Exception as e:
                            error_count += 1
                            logger.error(f"Error sending email to {data['email']}: {str(e)}")
                    
                    if success_count > 0:
                        self.message_user(request, f'Successfully sent {success_count} emails')
                    if error_count > 0:
                        self.message_user(request, f'Failed to send {error_count} emails. Check the logs for details.', level=messages.WARNING)
                else:
                    self.message_user(request, 'No valid email addresses found in the Excel file', level=messages.ERROR)

            except Exception as e:
                self.message_user(request, f'Error processing Excel file: {str(e)}', level=messages.ERROR)

            return HttpResponseRedirect('.')

        context = dict(
            self.admin_site.each_context(request),
            title='Send Bulk Email'
        )
        return render(request, 'admin/users/user/send_bulk_email.html', context)

    def send_bulk_email(self, request, queryset):
        return HttpResponseRedirect(reverse('admin:user-send-bulk-email'))
    send_bulk_email.short_description = "Send bulk email using Excel file"

admin.site.register(User, CustomUserAdmin)