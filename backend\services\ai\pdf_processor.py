import PyPDF2  # Instead of fitz
import re
import logging

logger = logging.getLogger(__name__)

class PDFProcessor:
    """Simple PDF text extraction service"""
    
    def extract_text_from_pdf(self, pdf_path):
        """Extract text from PDF file using PyPDF2"""
        try:
            text = ""
            
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                for page in reader.pages:
                    text += page.extract_text()
            
            # Clean the text
            cleaned_text = self.clean_text(text)
            
            if not cleaned_text.strip():
                raise Exception("No readable text found in PDF")
                
            logger.info(f"Extracted {len(cleaned_text)} characters from PDF")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"PDF extraction failed: {e}")
            raise Exception(f"Could not read PDF: {str(e)}")
    
    def clean_text(self, text):
        """Basic text cleaning"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove problematic characters
        text = re.sub(r'[^\w\s\.\,\!\?\:\;\-\(\)\[\]\/\\\"\']', ' ', text)
        
        return text.strip()