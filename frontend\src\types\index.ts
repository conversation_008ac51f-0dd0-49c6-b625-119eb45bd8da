export interface Track {
  id: number;
  name: string;
  description: string;
}



export interface Course {
  code: string;
  name: string;
  description: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  points: number;
  level: number;
  profile_picture?: string;
  is_approved: boolean;
  is_email_verified: boolean;
  track: Track;
  first_name: string;
  last_name: string;
  phone_number?: string;
  current_course?: Course;
  user_type: "S" | "E";
}

export interface Question {
  id: number;
  text: string;
  response: boolean;
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: number;
  title: string;
  description: string;
  pdf_file: string;
  points_required: number;
  level_required: number;
  required_evaluators: number;
  created_at: string;
  updated_at: string;
  track: Track;
  questions: Question[];
  can_submit: boolean;
}

export interface ProjectSubmission {
  id: number;
  project: Project;
  submitted_by: User;
  github_repo: string | null;
  zip_file: string | null;
  submission_type: "github" | "zip";
  status: "pending" | "in_evaluation" | "completed" | "failed";
  created_at: string;
  updated_at: string;
  evaluation?: {
    comments: string;
    is_approved: boolean;
    score: number; // percentage of yes responses
    created_at: string;
  };
  evaluations?: Evaluation[];
  evaluation_progress: {
    current: number;
    required: number;
    is_complete: boolean;
  };
  final_score: number | null;
  assigned_evaluator: User | null;
}

export interface QuestionResponse {
  question_id: number;
  response: boolean;
  submission_type: "github" | "zip";
  zip_file: string | null;
}

export interface Evaluation {
  id: number;
  submission: ProjectSubmission;
  evaluator: User;
  comments: string;
  is_approved: boolean;
  score: number;
  created_at: string;
  question_responses: QuestionResponse[];
}
