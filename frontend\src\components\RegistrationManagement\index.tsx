import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Chip,
  useTheme,
  Divider,
  Button,
  Collapse,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
} from '@mui/material';
import {
  PendingActions as PendingIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import api from '../../services/api';
import { RegistrationRequest } from '../RegistrationRequestCard';

const RegistrationManagement: React.FC = () => {
  const theme = useTheme();
  const [requests, setRequests] = useState<RegistrationRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchRequests = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);
      
      const response = await api.get('/projects/courses/registrations/pending/');
      setRequests(response.data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to fetch registration requests');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const handleStatusChange = (requestId: number) => {
    // Remove the request from the list since it's no longer pending
    setRequests(prev => prev.filter(req => req.id !== requestId));
  };

  const handleApprove = async (requestId: number) => {
    try {
      await api.post(`/projects/courses/registrations/${requestId}/approve/`);
      handleStatusChange(requestId);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to approve request');
    }
  };

  const handleReject = async (requestId: number) => {
    try {
      await api.post(`/projects/courses/registrations/${requestId}/reject/`);
      handleStatusChange(requestId);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to reject request');
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleRefresh = () => {
    fetchRequests(true);
  };

  const pendingCount = requests.length;

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper
      sx={{
        background: 'rgba(17, 34, 64, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 3,
          borderBottom: `1px solid ${theme.palette.divider}`,
          cursor: 'pointer',
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <PendingIcon sx={{ color: theme.palette.warning.main, fontSize: 28 }} />
            <Box>
              <Typography variant="h6" sx={{ color: 'text.primary', fontWeight: 600 }}>
                Pending Registration Requests
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Students waiting for approval to join your courses
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {pendingCount > 0 && (
              <Chip
                label={`${pendingCount} pending`}
                size="small"
                sx={{
                  backgroundColor: 'rgba(255, 184, 108, 0.1)',
                  color: theme.palette.warning.main,
                }}
              />
            )}
            <Button
              size="small"
              startIcon={refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
              onClick={(e) => {
                e.stopPropagation();
                handleRefresh();
              }}
              disabled={refreshing}
              sx={{ color: 'text.secondary' }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            {expanded ? (
              <ExpandLessIcon sx={{ color: 'text.secondary' }} />
            ) : (
              <ExpandMoreIcon sx={{ color: 'text.secondary' }} />
            )}
          </Box>
        </Box>
      </Box>

      {/* Content */}
      <Collapse in={expanded}>
        <Box sx={{ p: 3 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {pendingCount === 0 ? (
            <Box
              sx={{
                textAlign: 'center',
                py: 6,
                color: 'text.secondary',
              }}
            >
              <PendingIcon sx={{ fontSize: 64, mb: 2, opacity: 0.3 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                No Pending Requests
              </Typography>
              <Typography variant="body2">
                All registration requests have been processed, or no students have requested to join your courses yet.
              </Typography>
            </Box>
          ) : (
            <>
              <Typography variant="body2" sx={{ color: 'text.secondary', mb: 3 }}>
                {pendingCount} student{pendingCount !== 1 ? 's' : ''} waiting for your approval
              </Typography>

              <List sx={{ width: '100%', bgcolor: 'transparent' }}>
                {requests.map((request, index) => (
                  <React.Fragment key={request.id}>
                    <ListItem
                      sx={{
                        py: 2,
                        px: 0,
                        '&:hover': {
                          backgroundColor: 'rgba(100, 255, 218, 0.05)',
                          borderRadius: 1,
                        },
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: theme.palette.primary.main,
                          mr: 2,
                          width: 40,
                          height: 40,
                        }}
                      >
                        {getInitials(request.student_name)}
                      </Avatar>

                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <Typography variant="subtitle1" sx={{ color: 'text.primary', fontWeight: 600 }}>
                              {request.student.username}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <SchoolIcon sx={{ fontSize: 16, color: theme.palette.secondary.main }} />
                              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                {request.course.code}
                              </Typography>
                            </Box>
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" sx={{ color: 'text.secondary', mt: 0.5 }}>
                            {request.student_name} • {request.course.name}
                          </Typography>
                        }
                      />

                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            size="small"
                            variant="contained"
                            startIcon={<ApproveIcon />}
                            onClick={() => handleApprove(request.id)}
                            sx={{
                              minWidth: 100,
                              background: 'linear-gradient(45deg, #4CAF50, #66BB6A)',
                              '&:hover': {
                                background: 'linear-gradient(45deg, #45A049, #5CB85C)',
                              },
                            }}
                          >
                            Approve
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            startIcon={<RejectIcon />}
                            onClick={() => handleReject(request.id)}
                            sx={{
                              minWidth: 100,
                              borderColor: theme.palette.error.main,
                              color: theme.palette.error.main,
                              '&:hover': {
                                borderColor: theme.palette.error.dark,
                                backgroundColor: 'rgba(244, 67, 54, 0.1)',
                              },
                            }}
                          >
                            Reject
                          </Button>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < requests.length - 1 && (
                      <Divider sx={{ bgcolor: 'rgba(100, 255, 218, 0.1)' }} />
                    )}
                  </React.Fragment>
                ))}
              </List>
            </>
          )}
        </Box>
      </Collapse>
    </Paper>
  );
};

export default RegistrationManagement;
