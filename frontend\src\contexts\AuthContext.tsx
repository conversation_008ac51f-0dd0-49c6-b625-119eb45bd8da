import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  useCallback,
} from "react";
import api from "../services/api";
import { User } from "../types/index";

interface AuthContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  isAuthenticated: boolean;
  login: (credentials: { email: string; password: string }) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  setUserAndToken: (userData: any, token: string) => void;
  checkAuth: () => Promise<void>;
  fetchUser: () => Promise<User | null>;
  refreshUserData: () => Promise<User | null>;
}

export const AuthContext = createContext<AuthContextType>({
  user: null,
  setUser: () => {},
  isAuthenticated: false,
  login: async () => ({ success: false }),
  logout: () => {},
  setUserAndToken: () => {},
  checkAuth: async () => {},
  fetchUser: async () => null,
  refreshUserData: async () => null,
});

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Initialize user state from localStorage with error handling
  const [user, setUser] = useState<User | null>(() => {
    try {
      const savedUser = localStorage.getItem("user");
      return savedUser ? JSON.parse(savedUser) : null;
    } catch (error) {
      localStorage.removeItem("user");
      return null;
    }
  });

  const setUserAndToken = useCallback((userData: User, token: string) => {
    try {
      localStorage.setItem("user", JSON.stringify(userData));
      localStorage.setItem("token", token);
      setUser(userData);
      api.defaults.headers.common["Authorization"] = `Token ${token}`;
    } catch (error) {
    }
  }, []);

  const login = async (credentials: { email: string; password: string }) => {
    // Frontend validation
    if (!credentials.email || !credentials.password) {
      return { 
        success: false, 
        error: "Please enter both email and password" 
      };
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(credentials.email)) {
      return {
        success: false,
        error: "Please enter a valid email address"
      };
    }

    // Password length validation
    if (credentials.password.length < 6) {
      return {
        success: false,
        error: "Password must be at least 6 characters long"
      };
    }

    try {
      const response = await api.post("/users/login/", credentials);
      if (response.data.token) {
        setUserAndToken(response.data.user, response.data.token);
        return { success: true };
      }
      return { success: false, error: "Invalid login credentials" };
    } catch (error: any) {
      // Handle network errors without showing them in console
      if (!error.response) {
        return { 
          success: false, 
          error: "Unable to connect to server. Please check your internet connection." 
        };
      }
      
      // Handle known error responses
      if (error.response.data?.error) {
        return { 
          success: false, 
          error: error.response.data.error 
        };
      }

      // Generic error
      return { 
        success: false, 
        error: "An error occurred. Please try again later." 
      };
    }
  };

  const logout = useCallback(() => {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    setUser(null);
    delete api.defaults.headers.common["Authorization"];
  }, []);

  const checkAuth = async () => {
    try {
      const response = await api.get("/users/profile/");
      if (response.status === 200) {
        setUser(response.data);
      }
    } catch (error) {
      logout();
    }
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      api.defaults.headers.common["Authorization"] = `Token ${token}`;
      checkAuth();
    }
  }, []);

  const fetchUser = async () => {
    try {
      const response = await api.get<User>("/users/profile/");
      if (response.status === 200 && response.data.is_approved) {
        setUser(response.data);
        localStorage.setItem("user", JSON.stringify(response.data));
        return response.data;
      } else {
        logout();
      }
    } catch (error) {
      logout();
    }
    return null;
  };

  const refreshUserData = async () => {
    return await fetchUser();
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        isAuthenticated: !!user,
        login,
        logout,
        setUserAndToken,
        checkAuth,
        fetchUser,
        refreshUserData,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
