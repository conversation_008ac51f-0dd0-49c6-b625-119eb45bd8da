import React from "react";
import {
  Box,
  Typography,
  IconButton,
  Paper,
  List,
  ListItem,
  ListItemSecondaryAction,
  TextField,
  Button,
  useTheme,
  MenuItem,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

const MAX_FILE_SIZE_MB = 10; // Maximum file size in MB
const ALLOWED_FILE_TYPES = ["application/pdf"];

interface PDFContent {
  id: string;
  file: File;
  title: string;
  description: string;
  prerequisiteId?: string | null;
}

interface PDFManagementProps {
  pdfs: PDFContent[];
  onPDFsChange: (pdfs: PDFContent[]) => void;
  errors: { [key: string]: string };
}

const validateFile = (file: File): string | null => {
  if (!ALLOWED_FILE_TYPES.includes(file.type)) {
    return "Only PDF files are allowed";
  }

  const fileSizeInMB = file.size / (1024 * 1024);
  if (fileSizeInMB > MAX_FILE_SIZE_MB) {
    return `File size must not exceed ${MAX_FILE_SIZE_MB}MB`;
  }

  return null;
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

const PDFPreview: React.FC<{ file: File }> = ({ file }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 1,
        p: 1,
        background: "rgba(100, 255, 218, 0.1)",
        borderRadius: 1,
        mt: 1,
      }}
    >
      <InsertDriveFileIcon sx={{ color: theme.palette.primary.main }} />
      <Box sx={{ flexGrow: 1 }}>
        <Typography
          variant="body2"
          sx={{ color: "text.primary", fontWeight: 500 }}
        >
          {file.name}
        </Typography>
        <Typography variant="caption" sx={{ color: "text.secondary" }}>
          {formatFileSize(file.size)}
        </Typography>
      </Box>
      <CheckCircleIcon sx={{ color: theme.palette.success.main }} />
    </Box>
  );
};

interface SortablePDFItemProps {
  pdf: PDFContent;
  pdfs: PDFContent[];
  onFieldChange: (id: string, field: "title" | "description" | "prerequisiteId", value: string) => void;
  onDelete: (id: string) => void;
}

const SortablePDFItem: React.FC<SortablePDFItemProps> = ({
  pdf,
  pdfs,
  onFieldChange,
  onDelete,
}) => {
  const theme = useTheme();
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: pdf.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <Paper
      ref={setNodeRef}
      style={style}
      sx={{
        mb: 2,
        background: "rgba(17, 34, 64, 0.95)",
        backdropFilter: "blur(10px)",
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
      }}
    >
      <ListItem>
        <Box
          {...attributes}
          {...listeners}
          sx={{
            mr: 2,
            cursor: isDragging ? 'grabbing' : 'grab',
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <DragIndicatorIcon />
        </Box>
        <Box sx={{ flexGrow: 1 }}>
          <TextField
            fullWidth
            label="Title"
            value={pdf.title}
            onChange={(e) => onFieldChange(pdf.id, "title", e.target.value)}
            sx={{ mb: 2 }}
          />
          <PDFPreview file={pdf.file} />
          <TextField
            fullWidth
            label="Description"
            value={pdf.description}
            onChange={(e) => onFieldChange(pdf.id, "description", e.target.value)}
            multiline
            rows={2}
            sx={{ mt: 2 }}
          />
          <TextField
            select
            fullWidth
            label="Prerequisite"
            value={pdf.prerequisiteId || ""}
            onChange={(e) => onFieldChange(pdf.id, "prerequisiteId", e.target.value)}
            sx={{ mt: 2 }}
          >
            <MenuItem value="">No prerequisite</MenuItem>
            {pdfs
              .filter((p) => p.id !== pdf.id)
              .map((p) => (
                <MenuItem key={p.id} value={p.id}>
                  {p.title}
                </MenuItem>
              ))}
          </TextField>
        </Box>
        <ListItemSecondaryAction>
          <IconButton
            edge="end"
            onClick={() => onDelete(pdf.id)}
            sx={{ color: theme.palette.error.main }}
          >
            <DeleteIcon />
          </IconButton>
        </ListItemSecondaryAction>
      </ListItem>
    </Paper>
  );
};

const PDFManagement: React.FC<PDFManagementProps> = ({
  pdfs,
  onPDFsChange,
  errors,
}) => {
  const theme = useTheme();
  const [validationError, setValidationError] = React.useState<string | null>(
    null
  );

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    setValidationError(null);

    if (files && files.length > 0) {
      // Validate each file
      for (const file of Array.from(files)) {
        const error = validateFile(file);
        if (error) {
          setValidationError(error);
          return;
        }
      }

      const newPdfs = Array.from(files).map((file) => ({
        id: Math.random().toString(36).substr(2, 9),
        file,
        title: file.name.replace(".pdf", ""),
        description: "",
        prerequisiteId: null,
      }));
      onPDFsChange([...pdfs, ...newPdfs]);
    }
  };

  const handleDelete = (id: string) => {
    onPDFsChange(pdfs.filter((pdf) => pdf.id !== id));
  };

  const handleFieldChange = (
    id: string,
    field: "title" | "description" | "prerequisiteId",
    value: string
  ) => {
    onPDFsChange(
      pdfs.map((pdf) => (pdf.id === id ? { ...pdf, [field]: value } : pdf))
    );
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = pdfs.findIndex((pdf) => pdf.id === active.id);
      const newIndex = pdfs.findIndex((pdf) => pdf.id === over?.id);

      onPDFsChange(arrayMove(pdfs, oldIndex, newIndex));
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <input
          accept=".pdf"
          style={{ display: "none" }}
          id="pdf-upload"
          multiple
          type="file"
          onChange={handleFileChange}
        />
        <label htmlFor="pdf-upload">
          <Button
            variant="outlined"
            component="span"
            startIcon={<FileUploadIcon />}
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              "&:hover": {
                borderColor: theme.palette.primary.dark,
                backgroundColor: "rgba(100, 255, 218, 0.1)",
              },
            }}
          >
            Upload PDFs
          </Button>
        </label>
        {(errors.pdfs || validationError) && (
          <Typography color="error" variant="caption" sx={{ ml: 2 }}>
            {errors.pdfs || validationError}
          </Typography>
        )}
      </Box>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={pdfs.map(pdf => pdf.id)} strategy={verticalListSortingStrategy}>
          <List sx={{ width: "100%" }}>
            {pdfs.map((pdf) => (
              <SortablePDFItem
                key={pdf.id}
                pdf={pdf}
                pdfs={pdfs}
                onFieldChange={handleFieldChange}
                onDelete={handleDelete}
              />
            ))}
          </List>
        </SortableContext>
      </DndContext>
    </Box>
  );
};

export default PDFManagement;
