import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from datetime import datetime
import pytz

def get_dubai_time(dt_str):
    """Convert UTC datetime string to Dubai timezone"""
    if not dt_str or dt_str == 'Never':
        return "Never"
    dubai_tz = pytz.timezone('Asia/Dubai')
    dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
    dubai_time = dt.astimezone(dubai_tz)
    return dubai_time.strftime('%Y-%m-%d %H:%M (UAE)')

def style_header_cell(cell):
    """Apply styling to header cells"""
    cell.font = Font(bold=True, color="FFFFFF")
    cell.fill = PatternFill("solid", start_color="1F4E78")
    cell.alignment = Alignment(horizontal="center", vertical="center")
    thin_border = Border(
        left=Side(style='thin'), 
        right=Side(style='thin'), 
        top=Side(style='thin'), 
        bottom=Side(style='thin')
    )
    cell.border = thin_border

def create_course_statistics_excel(course_stats, output_path):
    """
    Create an Excel file with course statistics.
    
    Args:
        course_stats (dict): Course statistics data
        output_path (str): Path where the Excel file will be saved
    """
    wb = openpyxl.Workbook()
    
    # Course Overview Sheet
    overview = wb.active
    overview.title = "Course Overview"
    
    # Header styling
    overview['A1'] = "Course Statistics Report"
    overview['A1'].font = Font(size=16, bold=True)
    overview.merge_cells('A1:D1')
    
    # Basic Information
    overview['A3'] = "Course Information"
    overview['A3'].font = Font(bold=True)
    
    info_data = [
        ("Name", course_stats["name"]),
        ("Code", course_stats["code"]),
        ("Description", course_stats["description"]),
        ("Educator", f"{course_stats['educator']['first_name']} {course_stats['educator']['last_name']}"),
        ("Educator Email", course_stats["educator"]["email"]),
        ("Total Students", course_stats["students_count"]),
        ("Total Projects", course_stats["projects_count"]),
        ("Completion Rate", f"{course_stats['completion_rate']:.2f}%"),
        ("Average Score", f"{course_stats['average_score']:.2f}%"),
        ("Active Submissions", course_stats["active_submissions"]),
        ("Completed Submissions", course_stats["completed_submissions"]),
        ("Created On", get_dubai_time(course_stats["created_at"])),
        ("Last Updated", get_dubai_time(course_stats["updated_at"])),
    ]
    
    for idx, (key, value) in enumerate(info_data, start=4):
        overview[f'A{idx}'] = key
        overview[f'B{idx}'] = value
        overview[f'A{idx}'].font = Font(bold=True)
    
    # Student Statistics Sheet
    students = wb.create_sheet("Student Statistics")
    
    # Headers for student statistics
    headers = [
        "Student Name",
        "Email",
        "Submissions Count",
        "Completed Projects",
        "Average Score",
        "Current Status",
        "Last Submission"
    ]
    
    for col, header in enumerate(headers, start=1):
        cell = students.cell(row=1, column=col)
        cell.value = header
        style_header_cell(cell)
    
    # Add student data if available
    if "student_statistics" in course_stats:
        for row, student in enumerate(course_stats["student_statistics"], start=2):
            students.cell(row=row, column=1).value = student["student_name"]
            students.cell(row=row, column=2).value = student["student_email"]
            students.cell(row=row, column=3).value = student["submissions_count"]
            students.cell(row=row, column=4).value = student["completed_projects"]
            students.cell(row=row, column=5).value = f"{student['average_score']:.2f}%"
            students.cell(row=row, column=6).value = student["current_status"]
            students.cell(row=row, column=7).value = get_dubai_time(student["last_submission"])
    else:
        cell = students.cell(row=2, column=1)
        cell.value = "No student statistics available"
        students.merge_cells('A2:G2')
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # Auto-adjust column widths
    for sheet in [overview, students]:
        for column in sheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)  # Cap width at 50
            sheet.column_dimensions[column_letter].width = adjusted_width
    
    # Save the workbook
    wb.save(output_path)