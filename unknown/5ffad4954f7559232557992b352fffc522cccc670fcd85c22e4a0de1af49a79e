import pandas as pd
import random

# Lists of sample names
# first_names = [
#     '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
#     '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>',
#     '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'
# ]

# last_names = [
#     '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
#     '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
#     '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>hare<PERSON>'
# ]

first_names = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
]

last_names = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
]

def generate_phone_number():
    # <PERSON>rate phone numbers in format: 0501234567 (exactly 10 digits)
    return f"050{str(random.randint(1000000, 9999999)).zfill(7)}"

def generate_dummy_data(num_users=20):
    data = []
    used_emails = set()
    used_phones = set()
    
    for i in range(num_users):
        while True:
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            email = f"test_user{i+100}@ata.com"
            phone_number = generate_phone_number()
            
            if email not in used_emails and phone_number not in used_phones:
                used_emails.add(email)
                used_phones.add(phone_number)
                data.append({
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'phone_number': phone_number
                })
                break
    return data

def create_excel_file(num_users=20, filename='dummy_users.xlsx'):
    # Generate dummy data
    users_data = generate_dummy_data(num_users)
    
    # Create DataFrame
    df = pd.DataFrame(users_data)
    
    # Save to Excel
    df.to_excel(filename, index=False)
    print(f"Created Excel file '{filename}' with {num_users} dummy users")

if __name__ == "__main__":
    create_excel_file(20, 'dummy_users.xlsx')