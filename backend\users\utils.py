
def validate_password(password, user_data=None):
    """Validate password meets requirements"""
    if len(password) < 8:
        return False, 'Password must be at least 8 characters long'
    if password.isdigit():
        return False, 'Password cannot be entirely numeric'
    if not any(c.isupper() for c in password):
        return False, 'Password must contain at least one uppercase letter'
    if not any(c.islower() for c in password):
        return False, 'Password must contain at least one lowercase letter'
    if not any(c.isdigit() for c in password):
        return False, 'Password must contain at least one number'
        
    # Check for common passwords
    common_passwords = ['password', 'password123', '12345678', 'qwerty123', 'admin123']
    if password.lower() in common_passwords:
        return False, 'This password is too common'
        
    # Check similarity with user data if provided
    if user_data:
        personal_info = [user_data.get('email', ''), 
                        user_data.get('first_name', ''), 
                        user_data.get('last_name', '')]
        for info in personal_info:
            if info and len(info) > 2 and info.lower() in password.lower():
                return False, 'Password cannot contain your personal information'

    return True, ''