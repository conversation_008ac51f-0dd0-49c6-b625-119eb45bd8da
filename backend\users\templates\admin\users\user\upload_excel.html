{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block content %}
<div>
    <h2>Upload Excel File to Create Users</h2>
    
    {% if messages %}
    <ul class="messagelist">
        {% for message in messages %}
        <li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</li>
        {% endfor %}
    </ul>
    {% endif %}

    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        <div style="margin-bottom: 20px;">
            <label for="excel_file">Excel File:</label>
            <input type="file" name="excel_file" id="excel_file" accept=".xlsx" required>
        </div>
        
        <div style="margin-bottom: 20px;">
            <label for="zone">Zone:</label>
            <select name="zone" id="zone" required>
                {% for zone in zones %}
                <option value="{{ zone.id }}">{{ zone.name }}</option>
                {% endfor %}
            </select>
        </div>
        
        <div style="margin-bottom: 20px;">
            <label for="track">Track:</label>
            <select name="track" id="track" required>
                {% for track in tracks %}
                <option value="{{ track.id }}">{{ track.name }}</option>
                {% endfor %}
            </select>
        </div>

        <input type="submit" value="Upload and Create Users" class="default" style="float: none">
    </form>
    
    <div style="margin-top: 20px;">
        <h3>Note:</h3>
        <p>The Excel file should contain the following columns:</p>
        <ul>
            <li>email (required)</li>
            <li>first_name (required)</li>
            <li>last_name (required)</li>
            <li>phone_number (optional)</li>
        </ul>
    </div>
</div>
{% endblock %}
