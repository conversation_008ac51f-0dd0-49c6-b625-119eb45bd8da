import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  List,
  ListItem,
  ListItemText,
  Typography,
  Button,
  Box,
  Paper,
  Divider,
  Grid2 as Grid,
  Card,
  CardContent,
  Chip,
  useTheme,
  Modal,
  IconButton,
  FormControl,
  TextField,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import AssignmentIcon from "@mui/icons-material/Assignment";
import EmojiEventsIcon from "@mui/icons-material/EmojiEvents";
import api from "../../services/api";
import { Course, Project, User } from "../../types/index";
import { useAuth } from "../../contexts/AuthContext";
import ProjectView from "@/components/ProjectView";
import SubmissionStatus from "@/components/SubmissionStatus";

interface ProjectInPool {
  id: number;
  project: Project;
  status: string;
  created_at: string;
  submitted_by: User;
  submitted_by_details: {
    id: number;
    username: string;
    email: string;
    full_name: string;
    track: string | null;
    submitted_at: string;
  };
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  useTheme();
  const { user, fetchUser } = useAuth();
  const [projects, setProjects] = useState<ProjectInPool[]>([]);
  const [loading, setLoading] = useState(true);
  const [nextProject, setNextProject] = useState<Project | null>(null);
  const [activeSubmissions, setActiveSubmissions] = useState<ProjectInPool[]>(
    []
  );
  const [openAddCourseModal, setOpenAddCourseModal] = useState(false);
  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<string>("");
  const [courseCode, setCourseCode] = useState("");
  const [registrationError, setRegistrationError] = useState<string>("");
  const [registrationSuccess, setRegistrationSuccess] = useState<string>("");
  const [isRegistering, setIsRegistering] = useState(false);

  useEffect(() => {
    fetchUser();
    fetchProjects();
    fetchActiveSubmissions();
    fetchAvailableCourses();
  }, []);

  useEffect(() => {
    if (selectedCourse) {
      fetchNextProject();
    }
  }, [selectedCourse]);

  // Sync selectedCourse with user's current_course when user data changes
  useEffect(() => {
    if (
      user?.current_course?.code &&
      user.current_course.code !== selectedCourse
    ) {
      setSelectedCourse(user.current_course.code);
    }
  }, [user?.current_course?.code]);

  const fetchAvailableCourses = async () => {
    try {
      const response = await api.get<Course[]>("/projects/courses/");
      setAvailableCourses(response.data);

      // Only set initial course selection if not already set
      if (!selectedCourse) {
        if (user?.current_course?.code) {
          setSelectedCourse(user.current_course.code);
        } else if (response.data.length > 0) {
          setSelectedCourse(response.data[0].code);
        }
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
      setAvailableCourses([]);
      setSelectedCourse("");
    } finally {
      setLoading(false);
    }
  };



  const fetchProjects = async () => {
    try {
      const response = await api.get<ProjectInPool[]>(`/projects/pool/`);
      setProjects(response.data);
    } catch (error) {
      console.error("Error fetching evaluation pool:", error);
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchNextProject = async () => {
    if (!selectedCourse) {
      setNextProject(null);
      return;
    }
    try {
      const response = await api.get(
        `/projects/courses/${selectedCourse}/next/`
      );
      setNextProject(response.data);
    } catch (error) {
      console.error("Error fetching next project:", error);
      setNextProject(null);
    } finally {
      setLoading(false);
    }
  };

  const handleEvaluate = async (projectId: number) => {
    try {
      navigate(`/evaluation/${projectId}`);
    } catch (error) {
      console.error("Error evaluating project:", error);
    }
  };

  const fetchActiveSubmissions = async () => {
    try {
      // const response = await api.get<ProjectInPool[]>(
      //   "/projects/my-submissions/"
      // );
      const response = await api.get<ProjectInPool[]>(
        "/projects/my-submissions/"
      );
      const activeSubmissionsData = response.data.filter((sub) =>
        ["pending", "in_evaluation"].includes(sub.status)
      );

      // Only update state if there are changes
      if (
        JSON.stringify(activeSubmissionsData) !==
        JSON.stringify(activeSubmissions)
      ) {
        setActiveSubmissions(activeSubmissionsData);
      }
    } catch (error) {
      console.error("Error fetching active submissions:", error);
    }
  };

  const handleProjectSubmitSuccess = async () => {
    await Promise.all([
      fetchUser(),
      fetchProjects(),
      fetchNextProject(),
      fetchActiveSubmissions(),
    ]);
  };

  const handleCourseRegister = async () => {
    if (!courseCode.trim()) {
      setRegistrationError("Please enter a course code");
      return;
    }

    setIsRegistering(true);
    setRegistrationError("");
    setRegistrationSuccess("");

    try {
      await api.post(
        `projects/courses/register/${courseCode.trim()}/`
      );
      await fetchAvailableCourses();
      setCourseCode("");
      setRegistrationSuccess("Registration request submitted successfully! Please wait for educator approval.");

      // Close modal after a short delay to show success message
      setTimeout(() => {
        setOpenAddCourseModal(false);
        setRegistrationSuccess("");
      }, 2000);
    } catch (error: any) {
      console.error("Error registering for course:", error);

      // Handle specific error cases
      if (error.response?.status === 404) {
        setRegistrationError("Invalid course code. Please check and try again.");
      } else if (error.response?.status === 409) {
        const message = error.response.data?.detail || "You have already registered for this course.";
        setRegistrationError(message);
      } else if (error.response?.status === 403) {
        setRegistrationError("Only students can register for courses.");
      } else if (error.response?.data?.detail) {
        setRegistrationError(error.response.data.detail);
      } else {
        setRegistrationError("Failed to register for course. Please try again.");
      }
    } finally {
      setIsRegistering(false);
    }
  };

  const handleCourseDropdownChange = async (courseCode: string) => {
    try {
      setSelectedCourse(courseCode);
      const response = await api.post(`/projects/courses/select/${courseCode}/`);
      console.log(response.data);
      await fetchUser(); // Refresh user data to update current course
      await fetchNextProject(); // Fetch next project for the new course
    } catch (error) {
      console.error("Error selecting course:", error);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      {/* User Stats Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              height: "100%",
              background:
                "linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(123, 137, 244, 0.15))",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(100, 255, 218, 0.2)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "text.primary" }}
              >
                Welcome back, {user?.first_name}!
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <EmojiEventsIcon
                  sx={{ fontSize: 40, mr: 2, color: "primary.main" }}
                />
                <Box>
                  <Typography variant="body2" sx={{ color: "text.secondary" }}>
                    Current Level
                  </Typography>
                  <Typography variant="h4" sx={{ color: "text.primary" }}>
                    {user?.level}
                  </Typography>
                </Box>
              </Box>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <Typography variant="body2" sx={{ color: "text.secondary", mr: 2 }}>
                  Current Course
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1, flex: 1 }}>
                  {availableCourses.length > 0 ? (
                    <>
                      <FormControl size="small" sx={{ minWidth: 200 }}>
                        <Select
                          value={user?.current_course?.code || selectedCourse || ""}
                          onChange={(e) => handleCourseDropdownChange(e.target.value)}
                          displayEmpty
                          sx={{
                            bgcolor: "rgba(123, 137, 244, 0.1)",
                            "& .MuiOutlinedInput-notchedOutline": {
                              borderColor: "rgba(123, 137, 244, 0.3)",
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                              borderColor: "rgba(123, 137, 244, 0.5)",
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                              borderColor: "primary.main",
                            },
                          }}
                        >
                          <MenuItem value="" disabled>
                            <em>Select a course</em>
                          </MenuItem>
                          {availableCourses.map((course) => (
                            <MenuItem key={course.code} value={course.code}>
                              {course.name} ({course.code})
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <IconButton
                        size="small"
                        onClick={() => setOpenAddCourseModal(true)}
                        sx={{
                          color: "secondary.main",
                          bgcolor: "rgba(100, 255, 218, 0.1)",
                          "&:hover": {
                            bgcolor: "rgba(100, 255, 218, 0.2)",
                          },
                        }}
                        title="Register for new course"
                      >
                        <AddIcon fontSize="small" />
                      </IconButton>
                    </>
                  ) : (
                    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                      <Typography variant="body2" sx={{ color: "text.secondary" }}>
                        No courses registered
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => setOpenAddCourseModal(true)}
                        sx={{
                          color: "secondary.main",
                          bgcolor: "rgba(100, 255, 218, 0.1)",
                          "&:hover": {
                            bgcolor: "rgba(100, 255, 218, 0.2)",
                          },
                        }}
                        title="Register for new course"
                      >
                        <AddIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  )}
                </Box>
              </Box>

              {/* Add Course Modal */}
              <Modal
                open={openAddCourseModal}
                onClose={() => setOpenAddCourseModal(false)}
                aria-labelledby="add-course-modal"
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: { xs: "90%", sm: 400 },
                    bgcolor: "background.paper",
                    borderRadius: 2,
                    boxShadow: 24,
                    p: 4,
                  }}
                >
                  <Typography variant="h6" component="h2" gutterBottom>
                    Register for New Course
                  </Typography>
                  <Typography variant="body2" sx={{ color: "text.secondary", mb: 3 }}>
                    Enter the course code provided by your educator to register for a new course.
                  </Typography>

                  {registrationError && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                      {registrationError}
                    </Alert>
                  )}

                  {registrationSuccess && (
                    <Alert severity="success" sx={{ mb: 2 }}>
                      {registrationSuccess}
                    </Alert>
                  )}

                  <Box sx={{ mt: 2 }}>
                    <TextField
                      fullWidth
                      label="Course Code"
                      value={courseCode}
                      onChange={(e) => {
                        setCourseCode(e.target.value);
                        // Clear errors when user starts typing
                        if (registrationError) setRegistrationError("");
                      }}
                      placeholder="Enter course code (e.g., CS101)"
                      sx={{ mb: 2 }}
                      autoFocus
                      error={!!registrationError}
                    />
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        gap: 2,
                      }}
                    >
                      <Button
                        onClick={() => {
                          setOpenAddCourseModal(false);
                          setCourseCode("");
                          setRegistrationError("");
                          setRegistrationSuccess("");
                        }}
                        disabled={isRegistering}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        onClick={handleCourseRegister}
                        disabled={!courseCode.trim() || isRegistering}
                        sx={{
                          background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
                          color: "#0A192F",
                          "&:hover": {
                            background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
                          },
                          minWidth: "100px",
                        }}
                      >
                        {isRegistering ? (
                          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            <CircularProgress size={16} color="inherit" />
                            Registering...
                          </Box>
                        ) : (
                          "Register"
                        )}
                      </Button>
                    </Box>
                  </Box>
                </Box>
              </Modal>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              height: "100%",
              background:
                "linear-gradient(135deg, rgba(123, 137, 244, 0.15), rgba(100, 255, 218, 0.15))",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(123, 137, 244, 0.2)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "text.primary" }}
              >
                Available Points
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <AssignmentIcon
                  sx={{ fontSize: 40, mr: 2, color: "secondary.main" }}
                />
                <Typography variant="h4" sx={{ color: "text.primary" }}>
                  {user?.points}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card
            sx={{
              height: "100%",
              background:
                "linear-gradient(135deg, rgba(100, 255, 218, 0.15), rgba(100, 255, 218, 0.05))",
              backdropFilter: "blur(10px)",
              border: "1px solid rgba(100, 255, 218, 0.2)",
            }}
          >
            <CardContent>
              <Typography
                variant="h6"
                gutterBottom
                sx={{ color: "text.primary" }}
              >
                Projects to Evaluate
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                <AssignmentIcon
                  sx={{ fontSize: 40, mr: 2, color: "success.light" }}
                />
                <Typography variant="h4" sx={{ color: "text.primary" }}>
                  {projects.length}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Active Submissions Section */}
      {activeSubmissions.length > 0 && (
        <Paper
          elevation={2}
          sx={{
            p: 4,
            mt: 4,
            background:
              "linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(123, 137, 244, 0.1))",
            border: "1px solid rgba(100, 255, 218, 0.2)",
          }}
        >
          <Typography variant="h5" gutterBottom>
            Active Submissions
          </Typography>
          {activeSubmissions.map((submission) => (
            <Box key={submission.id} sx={{ mb: 3 }}>
              <Typography variant="h6" color="primary" gutterBottom>
                {submission.project.title}
              </Typography>
              <SubmissionStatus
                submissionId={submission.id}
                onStatusChange={fetchActiveSubmissions}
              />
            </Box>
          ))}
        </Paper>
      )}

      {/* Next Project Box */}
      <Paper
        elevation={2}
        sx={{
          p: 4,
          mt: 4,
          background:
            "linear-gradient(135deg, rgba(100, 255, 218, 0.1), rgba(123, 137, 244, 0.1))",
          border: "1px solid rgba(100, 255, 218, 0.2)",
        }}
      >
        <Typography variant="h5" gutterBottom>
          Next Available Project
        </Typography>
        {nextProject ? (
          <ProjectView
            project={nextProject}
            onSubmit={fetchNextProject}
            onSubmitSuccess={handleProjectSubmitSuccess}
          />
        ) : (
          <Typography variant="body1" color="text.secondary">
            No projects available at your current level. Keep evaluating to
            level up!
          </Typography>
        )}
      </Paper>

      {/* Evaluation Pool Section */}
      <Paper elevation={2} sx={{ p: 3, mt: 4 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 3,
          }}
        >
          <Typography variant="h5" component="h2">
            Evaluation Pool
          </Typography>
          <Chip
            label={`${projects.length} Submissions Available`}
            color="primary"
            variant="outlined"
          />
        </Box>
        <Divider sx={{ mb: 2 }} />
        <List>
          {projects.map((submission, index) => (
            <React.Fragment key={submission.id}>
              {index > 0 && <Divider />}
              <ListItem
                sx={{
                  py: 2,
                  "&:hover": {
                    bgcolor: "action.hover",
                  },
                }}
              >
                <ListItemText
                  primary={
                    <Typography variant="h6" component="div">
                      {submission.project?.title || "Unknown Project"}
                    </Typography>
                  }
                  secondary={
                    <Typography component="div">
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: 1,
                          mt: 1,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            gap: 1,
                            alignItems: "center",
                          }}
                        >
                          <Chip
                            label={`${
                              submission.project?.points_required || 0
                            } Points Required`}
                            size="small"
                            color="secondary"
                          />
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            component="span"
                          >
                            Submitted by{" "}
                            {submission.submitted_by_details.full_name}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            display: "flex",
                            gap: 2,
                            alignItems: "center",
                          }}
                        >
                          <Typography
                            variant="caption"
                            color="text.secondary"
                            component="span"
                          >
                            Submitted:{" "}
                            {new Date(
                              submission.submitted_by_details.submitted_at
                            ).toLocaleDateString()}
                          </Typography>
                          <Box sx={{ display: "flex", gap: 1 }}>
                            {submission.submitted_by_details.track && (
                              <Chip
                                label={`Track: ${submission.submitted_by_details.track}`}
                                size="small"
                                variant="outlined"
                                color="secondary"
                              />
                            )}
                          </Box>
                        </Box>
                      </Box>
                    </Typography>
                  }
                />
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => handleEvaluate(submission.id)}
                  disabled={submission.submitted_by.id === user?.id}
                  sx={{ ml: 2, minWidth: 120 }}
                >
                  {submission.submitted_by.id === user?.id
                    ? "Your Submission"
                    : "Evaluate"}
                </Button>
              </ListItem>
            </React.Fragment>
          ))}
          {projects.length === 0 && (
            <ListItem>
              <ListItemText
                primary={
                  <Box
                    component="div"
                    sx={{
                      color: "text.secondary",
                      typography: "body1",
                      textAlign: "center",
                    }}
                  >
                    No submissions available for evaluation
                  </Box>
                }
              />
            </ListItem>
          )}
        </List>
      </Paper>
    </>
  );
};

export default Dashboard;
