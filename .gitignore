# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp/
.pnp.js

# Testing
coverage/
.coverage
htmlcov/
.tox/
.nox/
.pytest_cache/

# Production build
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-workspace
*.sublime-project

# Docker
.docker/
docker-compose.override.yml

# Misc
*.bak
*.tmp
*.temp
.cache/
*.pid
*.seed
*.pid.lock

# Frontend specific
frontend/.pnp
frontend/.pnp.js
frontend/coverage
frontend/build
frontend/.DS_Store
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*

# Backend specific
backend/static/
backend/media/
backend/*.pyc
backend/__pycache__/
backend/*.pyo
backend/*.pyd
backend/.Python
backend/*.db
backend/local_settings.py

# Environment files
.env
.env.local
.env.*.local

# But keep examples
!.env.example
!.env.production.example

# Build directories
dist/
build/

# Dependencies
node_modules/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories
.idea/
.vscode/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Cloudflare certificates
backend/nginx/ssl/cloudflare/*
!backend/nginx/ssl/cloudflare/.gitkeep

# Reports
reports/
