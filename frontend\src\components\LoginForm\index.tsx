import React, { useState } from 'react';
import { TextField, Button, Box, Alert, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const LoginForm: React.FC = () => {
  const navigate = useNavigate();
  const { login } = useAuth();
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    if (!formData.username.trim()) {
      setError('Username is required');
      return false;
    }
    if (!formData.password) {
      setError('Password is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await login({ email: formData.username, password: formData.password });
      navigate('/dashboard');
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 
                          err.response?.data?.message || 
                          'Invalid email or password';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%' }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      
      <TextField
        fullWidth
        label="Username"
        value={formData.username}
        onChange={(e) => {
          setFormData({ ...formData, username: e.target.value });
          setError(null);
        }}
        margin="normal"
        error={Boolean(error && !formData.username)}
        disabled={isLoading}
      />
      
      <TextField
        fullWidth
        label="Password"
        type="password"
        value={formData.password}
        onChange={(e) => {
          setFormData({ ...formData, password: e.target.value });
          setError(null);
        }}
        margin="normal"
        error={Boolean(error && !formData.password)}
        disabled={isLoading}
      />
      
      <Button
        type="submit"
        fullWidth
        variant="contained"
        disabled={isLoading}
        sx={{
          mt: 3,
          mb: 2,
          background: 'linear-gradient(45deg, #64FFDA, #7B89F4)',
          '&:hover': {
            background: 'linear-gradient(45deg, #5A6AD4, #A5B4FF)',
          },
        }}
      >
        {isLoading ? 'Logging in...' : 'Login'}
      </Button>

      <Typography 
        variant="body2" 
        align="center" 
        sx={{ mt: 2, color: 'text.secondary' }}
      >
        Don't have an account?{' '}
        <Button
          onClick={() => navigate('/register')}
          sx={{ 
            color: '#64FFDA',
            textTransform: 'none',
            '&:hover': {
              background: 'transparent',
              color: '#7B89F4',
            }
          }}
        >
          Register here
        </Button>
      </Typography>
    </Box>
  );
};

export default LoginForm; 