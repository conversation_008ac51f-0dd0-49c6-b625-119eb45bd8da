import pytest
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from users.models import Track
from projects.models import Course, CourseRegistrationRequest

User = get_user_model()

@pytest.fixture
def track():
    return Track.objects.create(name="Test Track")

@pytest.fixture
def student():
    return User.objects.create_user(
        username="student",
        email="<EMAIL>",
        password="testpass123",
        user_type='S'
    )

@pytest.fixture
def educator():
    return User.objects.create_user(
        username="educator",
        email="<EMAIL>",
        password="testpass123",
        user_type='E'
    )

@pytest.mark.django_db(transaction=True)
class TestUserTypes:
    def test_user_type_choices(self):
        """Test that user type is restricted to Student or Educator"""
        # Student creation should work
        student = User.objects.create_user(
            username="test_student",
            email="<EMAIL>",
            password="testpass123",
            user_type='S'
        )
        assert student.user_type == 'S'

        # Educator creation should work
        educator = User.objects.create_user(
            username="test_educator",
            email="<EMAIL>",
            password="testpass123",
            user_type='E'
        )
        assert educator.user_type == 'E'

        # Invalid user type should raise validation error
        invalid_user = User(
            username="test_invalid",
            email="<EMAIL>",
            password="testpass123",
            user_type='X'
        )
        with pytest.raises(ValidationError):
            invalid_user.full_clean()

    def test_educator_course_creation(self, educator, track):
        """Test that only educators can create courses"""

        course = Course.objects.create(
            name="Test Course",
            description="Test Description",
            educator=educator,
            track=track
        )
        assert course.educator == educator
        assert course in educator.courses.all()

    def test_student_course_enrollment(self, student, track, educator):
        """Test student course enrollment functionality"""

        # Create a course
        course = Course.objects.create(
            name="Test Course",
            description="Test Description",
            educator=educator,
            track=track
        )

        # Create registration request
        request = CourseRegistrationRequest.objects.create(
            student=student,
            course=course,
            status='pending'
        )

        # Approve registration
        request.status = 'approved'
        request.save()
        student.enrolled_courses.add(course)

        assert course in student.enrolled_courses.all()
