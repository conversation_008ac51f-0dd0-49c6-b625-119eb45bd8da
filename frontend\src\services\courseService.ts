import { EvaluationSheet } from "@/components/EvaluationSheetManagement";
import api from "./api";

export interface ProjectData {
	id: string; // Frontend-generated ID for tracking
	file: File;
	title: string;
	description: string;
	prerequisiteId?: string | null;
}

export interface CreateClassRequest {
	name: string;
	description: string;
	pdfs: Array<ProjectData>;
	evaluationSheet: EvaluationSheet;
}

export interface CreateClassResponse {
	code: string;
	name: string;
	description: string;
	status: string;
}

class CourseService {
	async createCourse(data: CreateClassRequest): Promise<CreateClassResponse> {
		let courseResponse;

		try {
			// First, create the course with basic info
			courseResponse = await api.post<CreateClassResponse>(
				"/projects/courses/",
				{
					name: data.name,
					description: data.description,
				}
			);
		} catch (error: any) {
			if (error.response?.data?.detail) {
				throw new Error(error.response.data.detail);
			} else if (error.response?.status === 400) {
				throw new Error(
					"Please check your course information and try again."
				);
			} else {
				throw new Error(
					"An error occurred while creating the course. Please try again."
				);
			}
		}

		const courseCode = courseResponse!.data.code;

		// Create projects one by one with their PDFs
		const projectIds = new Map<string, number>(); // Map from frontend ID to backend ID

		try {
			for (const projectData of data.pdfs) {
				const formData = new FormData();
				formData.append("title", projectData.title);
				formData.append("description", projectData.description);
				formData.append("pdf_file", projectData.file);
				formData.append("course", courseCode);

				if (
					projectData.prerequisiteId &&
					projectIds.has(projectData.prerequisiteId)
				) {
					formData.append(
						"prerequisite",
						String(projectIds.get(projectData.prerequisiteId))
					);
				}

				try {
					const projectResponse = await api.post(
						`/projects/courses/${courseCode}/projects/`,
						formData,
						{
							headers: {
								"Content-Type": "multipart/form-data",
							},
						}
					);

					// Store the mapping from frontend ID to backend ID
					projectIds.set(projectData.id, projectResponse.data.id);
				} catch (error: any) {
					throw new Error(
						`Failed to create project "${projectData.title}": ${
							error.response?.data?.detail ||
							error.response?.data?.pdf_file?.[0] ||
							"Please check the project information and try again."
						}`
					);
				}
			}

			// Create questions for each project
			for (const evaluation of data.evaluationSheet.pdfEvaluations) {
				const projectId = projectIds.get(evaluation.pdfId);
				if (!projectId) continue;

				try {
					// Create all questions for this project
					await Promise.all(
						evaluation.questions.map(async (question) => {
							try {
								await api.post(
									`/projects/courses/${courseCode}/projects/${projectId}/questions/`,
									{
										text: question.text,
										weight: question.weight,
										response: true, // "yes" is always the correct answer
									}
								);
							} catch (error: any) {
								throw new Error(
									`Failed to create question "${question.text.substring(
										0,
										30
									)}...": ${
										error.response?.data?.detail ||
										"Please check the question details and try again."
									}`
								);
							}
						})
					);
				} catch (error) {
					throw new Error(
						`Failed to create evaluation questions: ${
							error instanceof Error
								? error.message
								: "Unknown error"
						}`
					);
				}
			}
		} catch (error) {
			// If project or question creation fails, we should probably delete the course
			try {
				await api.delete(`/projects/courses/${courseCode}/`);
			} catch {
				// If cleanup fails, just log it - we'll show the original error to the user
				console.error("Failed to clean up course after error");
			}
			throw error;
		}

		return courseResponse.data;
	}

	async generateQuestionsFromPDFFile(file: File, title: string, description: string, numQuestions: number = 5): Promise<any> {
		
		const formData = new FormData();
		formData.append('pdf_file', file);
		formData.append('project_title', title);
		formData.append('project_description', description);
		formData.append('num_questions', String(numQuestions));
		
		const response = await api.post('/projects/generate-questions-from-pdf/', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		});
		return response.data;
	}

	async refineQuestionsFromPDFFile(file: File, title: string, description: string, numQuestions: number, userInstructions: string, previousQuestions: any[]): Promise<any> {
		const formData = new FormData();
		formData.append('pdf_file', file);
		formData.append('project_title', title);
		formData.append('project_description', description);
		formData.append('num_questions', String(numQuestions));
		formData.append('user_instructions', userInstructions);
		formData.append('previous_questions', JSON.stringify(previousQuestions));
		
		const response = await api.post('/projects/refine-questions-from-pdf/', formData, {
			headers: {
				'Content-Type': 'multipart/form-data',
			},
		});
		return response.data;
	}
}

export const courseService = new CourseService();
