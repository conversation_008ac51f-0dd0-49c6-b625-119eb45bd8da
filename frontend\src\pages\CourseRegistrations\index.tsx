import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  CircularProgress,
  useTheme,
  Checkbox,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Avatar,
  Divider,
} from "@mui/material";
import {
  ArrowBack as BackIcon,
  PendingActions as PendingIcon,
  CheckCircle as ApproveAllIcon,
  Cancel as RejectAllIcon,
  Refresh as RefreshIcon,
  SelectAll as SelectAllIcon,
  People as PeopleIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  School as SchoolIcon,
  PersonRemove as UnregisterIcon,
} from "@mui/icons-material";
import api from "../../services/api";
import { RegistrationRequest } from "../../components/RegistrationRequestCard";
import { EnrolledStudent } from "../../components/EnrolledStudentCard";

interface Course {
  code: string;
  name: string;
  description: string;
}

const CourseRegistrations: React.FC = () => {
  const { courseCode } = useParams<{ courseCode: string }>();
  const navigate = useNavigate();
  const theme = useTheme();

  const [course, setCourse] = useState<Course | null>(null);
  const [requests, setRequests] = useState<RegistrationRequest[]>([]);
  const [enrolledStudents, setEnrolledStudents] = useState<EnrolledStudent[]>(
    []
  );
  const [enrolledCount, setEnrolledCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [selectedRequests, setSelectedRequests] = useState<Set<number>>(new Set());
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  const fetchCourseAndRequests = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch course details and registration requests in parallel
      const [courseResponse, requestsResponse] = await Promise.all([
        api.get(`/projects/courses/${courseCode}/`),
        api.get(`/projects/courses/${courseCode}/registrations/`),
      ]);

      setCourse(courseResponse.data);

      // Handle the new API response structure
      if (requestsResponse.data.registration_requests) {
        // New API format with separate arrays
        setRequests(
          requestsResponse.data.registration_requests.filter(
            (req: RegistrationRequest) => req.status === "pending"
          )
        );
        setEnrolledStudents(requestsResponse.data.enrolled_students || []);
        setEnrolledCount(requestsResponse.data.enrolled_count || 0);
      } else {
        // Fallback for old API format
        setRequests(
          requestsResponse.data.filter(
            (req: RegistrationRequest) => req.status === "pending"
          )
        );
      }
    } catch (err: any) {
      setError(
        err.response?.data?.detail || "Failed to fetch course registration data"
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (courseCode) {
      fetchCourseAndRequests();
    }
  }, [courseCode]);

  const handleStatusChange = (
    requestId: number,
    newStatus: "approved" | "rejected"
  ) => {
    // Remove the request from the list since it's no longer pending
    setRequests((prev) => prev.filter((req) => req.id !== requestId));
    // Remove from selected if it was selected
    setSelectedRequests((prev) => {
      const newSet = new Set(prev);
      newSet.delete(requestId);
      return newSet;
    });

    // If approved, refresh the data to update enrolled students list
    if (newStatus === "approved") {
      fetchCourseAndRequests();
    }
  };

  const handleSelectRequest = (requestId: number, checked: boolean) => {
    setSelectedRequests((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(requestId);
      } else {
        newSet.delete(requestId);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectedRequests.size === requests.length) {
      // Deselect all
      setSelectedRequests(new Set());
    } else {
      // Select all
      setSelectedRequests(new Set(requests.map((req) => req.id)));
    }
  };

  const handleBulkAction = async (action: "approve" | "reject") => {
    if (selectedRequests.size === 0) return;

    setBulkActionLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const response = await api.post('/projects/courses/registrations/bulk-action/', {
        action,
        request_ids: Array.from(selectedRequests),
      });

      // Remove processed requests from the list
      const processedCount = selectedRequests.size;
      setRequests(prev => prev.filter(req => !selectedRequests.has(req.id)));
      setSelectedRequests(new Set());

      // Show success message
      const actionText = action === 'approve' ? 'approved' : 'rejected';
      setSuccessMessage(response.data?.detail || `${processedCount} student${processedCount !== 1 ? 's' : ''} ${actionText} successfully!`);

      // If any requests were approved, refresh the data to update enrolled students
      if (action === "approve") {
        fetchCourseAndRequests();
      }

      // Clear success message after 5 seconds
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err: any) {
      setError(
        err.response?.data?.detail || `Failed to ${action} selected requests`
      );
    } finally {
      setBulkActionLoading(false);
    }
  };

  const handleApprove = async (requestId: number) => {
    try {
      await api.post(`/projects/courses/registrations/${requestId}/approve/`);
      handleStatusChange(requestId, 'approved');

      // Show success message
      setSuccessMessage('Student approved successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);

      // Trigger notification update for real-time systems
      try {
        await api.post('/projects/courses/registrations/notifications/trigger/', {
          action: 'approve',
          request_id: requestId
        });
      } catch (notifErr) {
        console.warn('Failed to trigger notification update:', notifErr);
      }

      // Refresh data to update enrolled students
      fetchCourseAndRequests();
    } catch (err: any) {
      setError(err.response?.data?.detail || "Failed to approve request");
    }
  };

  const handleReject = async (requestId: number) => {
    try {
      await api.post(`/projects/courses/registrations/${requestId}/reject/`);
      handleStatusChange(requestId, 'rejected');

      // Show success message
      setSuccessMessage('Student registration rejected.');
      setTimeout(() => setSuccessMessage(null), 3000);

      // Trigger notification update for real-time systems
      try {
        await api.post('/projects/courses/registrations/notifications/trigger/', {
          action: 'reject',
          request_id: requestId
        });
      } catch (notifErr) {
        console.warn('Failed to trigger notification update:', notifErr);
      }
    } catch (err: any) {
      setError(err.response?.data?.detail || "Failed to reject request");
    }
  };

  const handleUnregister = async (studentId: number) => {
    try {
      await api.delete(
        `/projects/courses/${courseCode}/students/${studentId}/`
      );
      // Refresh data to update enrolled students
      fetchCourseAndRequests();
    } catch (err: any) {
      setError(err.response?.data?.detail || "Failed to unregister student");
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const pendingCount = requests.length;
  const selectedCount = selectedRequests.size;
  const allSelected = selectedCount === pendingCount && pendingCount > 0;

  if (loading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "60vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!course) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Course not found</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Button
          startIcon={<BackIcon />}
          onClick={() => navigate("/educator-dashboard")}
          sx={{
            mb: 2,
            color: "text.secondary",
            fontSize: { xs: "0.875rem", sm: "1rem" },
          }}
        >
          Back to Dashboard
        </Button>

        <Typography
          variant="h4"
          sx={{
            color: "text.primary",
            fontWeight: 600,
            mb: 1,
            fontSize: { xs: "1.5rem", sm: "2rem", md: "2.125rem" },
          }}
        >
          Registration Requests
        </Typography>
        <Typography
          variant="h6"
          sx={{
            color: "text.secondary",
            mb: 2,
            fontSize: { xs: "1rem", sm: "1.25rem" },
          }}
        >
          {course.name} ({course.code})
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: "text.secondary",
            fontSize: { xs: "0.875rem", sm: "1rem" },
          }}
        >
          {course.description}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {successMessage && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {successMessage}
        </Alert>
      )}

      {/* Stats and Actions Bar */}
      <Paper
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          background: "rgba(17, 34, 64, 0.95)",
          backdropFilter: "blur(10px)",
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: { xs: "column", sm: "row" },
            alignItems: { xs: "stretch", sm: "center" },
            justifyContent: "space-between",
            gap: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              alignItems: { xs: "flex-start", sm: "center" },
              gap: { xs: 2, sm: 4 },
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <PendingIcon
                sx={{
                  color: theme.palette.warning.main,
                  fontSize: { xs: 24, sm: 28 },
                }}
              />
              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    color: "text.primary",
                    fontSize: { xs: "1rem", sm: "1.25rem" },
                  }}
                >
                  {pendingCount} Pending Request{pendingCount !== 1 ? "s" : ""}
                </Typography>
                {selectedCount > 0 && (
                  <Typography
                    variant="body2"
                    sx={{
                      color: "text.secondary",
                      fontSize: { xs: "0.75rem", sm: "0.875rem" },
                    }}
                  >
                    {selectedCount} selected
                  </Typography>
                )}
              </Box>
            </Box>

            <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
              <PeopleIcon
                sx={{
                  color: theme.palette.secondary.main,
                  fontSize: { xs: 24, sm: 28 },
                }}
              />
              <Box>
                <Typography
                  variant="h6"
                  sx={{
                    color: "text.primary",
                    fontSize: { xs: "1rem", sm: "1.25rem" },
                  }}
                >
                  {enrolledCount} Enrolled Student
                  {enrolledCount !== 1 ? "s" : ""}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                  }}
                >
                  Currently in course
                </Typography>
              </Box>
            </Box>
          </Box>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: { xs: "center", sm: "flex-end" },
              flexWrap: "wrap",
              gap: 1,
              width: { xs: "100%", sm: "auto" },
            }}
          >
            {pendingCount > 0 && (
              <>
                <Tooltip title={allSelected ? "Deselect All" : "Select All"}>
                  <IconButton
                    onClick={handleSelectAll}
                    sx={{ color: "text.secondary" }}
                  >
                    <SelectAllIcon />
                  </IconButton>
                </Tooltip>

                {selectedCount > 0 && (
                  <Box
                    sx={{
                      display: "flex",
                      gap: 1,
                      flexDirection: { xs: "column", sm: "row" },
                      width: { xs: "100%", sm: "auto" },
                    }}
                  >
                    <Button
                      variant="contained"
                      size="small"
                      fullWidth={true}
                      startIcon={
                        bulkActionLoading ? (
                          <CircularProgress size={16} />
                        ) : (
                          <ApproveAllIcon />
                        )
                      }
                      onClick={() => handleBulkAction("approve")}
                      disabled={bulkActionLoading}
                      sx={{
                        background: "linear-gradient(45deg, #4CAF50, #66BB6A)",
                        "&:hover": {
                          background:
                            "linear-gradient(45deg, #45A049, #5CB85C)",
                        },
                        fontSize: { xs: "0.75rem", sm: "0.875rem" },
                        py: { xs: 1, sm: "auto" },
                      }}
                    >
                      Approve Selected ({selectedCount})
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      fullWidth={true}
                      startIcon={
                        bulkActionLoading ? (
                          <CircularProgress size={16} />
                        ) : (
                          <RejectAllIcon />
                        )
                      }
                      onClick={() => handleBulkAction("reject")}
                      disabled={bulkActionLoading}
                      sx={{
                        borderColor: theme.palette.error.main,
                        color: theme.palette.error.main,
                        "&:hover": {
                          borderColor: theme.palette.error.dark,
                          backgroundColor: "rgba(244, 67, 54, 0.1)",
                        },
                        fontSize: { xs: "0.75rem", sm: "0.875rem" },
                        py: { xs: 1, sm: "auto" },
                      }}
                    >
                      Reject Selected ({selectedCount})
                    </Button>
                  </Box>
                )}
              </>
            )}

            <Tooltip title="Refresh">
              <IconButton
                onClick={fetchCourseAndRequests}
                sx={{ color: "text.secondary" }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Paper>

      {/* Registration Requests */}
      {pendingCount === 0 ? (
        <Paper
          sx={{
            p: 6,
            textAlign: "center",
            background: "rgba(17, 34, 64, 0.95)",
            backdropFilter: "blur(10px)",
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <PendingIcon
            sx={{ fontSize: 64, mb: 2, opacity: 0.3, color: "text.secondary" }}
          />
          <Typography variant="h6" sx={{ mb: 1, color: "text.primary" }}>
            No Pending Requests
          </Typography>
          <Typography variant="body2" sx={{ color: "text.secondary" }}>
            All registration requests for this course have been processed.
          </Typography>
        </Paper>
      ) : (
        <Paper
          sx={{
            background: "rgba(17, 34, 64, 0.95)",
            backdropFilter: "blur(10px)",
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <List sx={{ width: "100%", bgcolor: "transparent" }}>
            {requests.map((request, index) => (
              <React.Fragment key={request.id}>
                <ListItem
                  sx={{
                    py: 2,
                    px: 3,
                    "&:hover": {
                      backgroundColor: "rgba(100, 255, 218, 0.05)",
                    },
                  }}
                >
                  <Checkbox
                    checked={selectedRequests.has(request.id)}
                    onChange={(e) =>
                      handleSelectRequest(request.id, e.target.checked)
                    }
                    sx={{ mr: 2 }}
                  />

                  <Avatar
                    sx={{
                      bgcolor: theme.palette.primary.main,
                      mr: 2,
                      width: 40,
                      height: 40,
                    }}
                  >
                    {getInitials(request.student_name)}
                  </Avatar>

                  <ListItemText
                    primary={
                      <Box
                        sx={{ display: "flex", alignItems: "center", gap: 2 }}
                      >
                        <Typography
                          variant="subtitle1"
                          sx={{ color: "text.primary", fontWeight: 600 }}
                        >
                          {request.student.username}
                        </Typography>
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <SchoolIcon
                            sx={{
                              fontSize: 16,
                              color: theme.palette.secondary.main,
                            }}
                          />
                          <Typography
                            variant="body2"
                            sx={{ color: "text.secondary" }}
                          >
                            {request.course.code}
                          </Typography>
                        </Box>
                      </Box>
                    }
                    secondary={
                      <Typography
                        variant="body2"
                        sx={{ color: "text.secondary", mt: 0.5 }}
                      >
                        {request.student_name} • {request.course.name}
                      </Typography>
                    }
                  />

                  <ListItemSecondaryAction
                    sx={{
                      position: { xs: "relative", sm: "absolute" },
                      transform: { xs: "none", sm: "translateY(-50%)" },
                      top: { xs: "auto", sm: "50%" },
                      right: { xs: 0, sm: 16 },
                      mt: { xs: 2, sm: 0 },
                      width: { xs: "100%", sm: "auto" },
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        gap: 1,
                        justifyContent: { xs: "center", sm: "flex-end" },
                        width: "100%",
                      }}
                    >
                      <Button
                        size="small"
                        variant="contained"
                        startIcon={<ApproveIcon />}
                        onClick={() => handleApprove(request.id)}
                        sx={{
                          width: { xs: "auto", sm: "auto" },
                          minWidth: { xs: 90, sm: 90 },
                          background:
                            "linear-gradient(45deg, #4CAF50, #66BB6A)",
                          "&:hover": {
                            background:
                              "linear-gradient(45deg, #45A049, #5CB85C)",
                          },
                          fontSize: { xs: "0.75rem", sm: "0.875rem" },
                          py: { xs: 1, sm: "auto" },
                        }}
                      >
                        Approve
                      </Button>
                      <Button
                        size="small"
                        variant="outlined"
                        startIcon={<RejectIcon />}
                        onClick={() => handleReject(request.id)}
                        sx={{
                          width: { xs: "auto", sm: "auto" },
                          minWidth: { xs: 90, sm: 90 },
                          borderColor: theme.palette.error.main,
                          color: theme.palette.error.main,
                          "&:hover": {
                            borderColor: theme.palette.error.dark,
                            backgroundColor: "rgba(244, 67, 54, 0.1)",
                          },
                          fontSize: { xs: "0.75rem", sm: "0.875rem" },
                          py: { xs: 1, sm: "auto" },
                        }}
                      >
                        Reject
                      </Button>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < requests.length - 1 && (
                  <Divider sx={{ bgcolor: "rgba(100, 255, 218, 0.1)" }} />
                )}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}

      {/* Enrolled Students Section */}
      <Box sx={{ mt: 6 }}>
        <Typography variant="h5" sx={{ mb: 3, color: "text.primary" }}>
          Enrolled Students ({enrolledCount})
        </Typography>

        {enrolledCount === 0 ? (
          <Paper
            sx={{
              p: 6,
              textAlign: "center",
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <PeopleIcon
              sx={{
                fontSize: 64,
                mb: 2,
                opacity: 0.3,
                color: "text.secondary",
              }}
            />
            <Typography variant="h6" sx={{ mb: 1, color: "text.primary" }}>
              No Enrolled Students
            </Typography>
            <Typography variant="body2" sx={{ color: "text.secondary" }}>
              No students have been enrolled in this course yet.
            </Typography>
          </Paper>
        ) : (
          <Paper
            sx={{
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <List sx={{ width: "100%", bgcolor: "transparent" }}>
              {enrolledStudents.map((student, index) => (
                <React.Fragment key={student.id}>
                  <ListItem
                    sx={{
                      py: 2,
                      px: 3,
                      "&:hover": {
                        backgroundColor: "rgba(100, 255, 218, 0.05)",
                      },
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: theme.palette.secondary.main,
                        mr: 2,
                        width: 40,
                        height: 40,
                      }}
                    >
                      {getInitials(
                        `${student.first_name} ${student.last_name}`
                      )}
                    </Avatar>

                    <ListItemText
                      primary={
                        <Box
                          sx={{
                            display: "flex",
                            flexDirection: { xs: "column", sm: "row" },
                            alignItems: { xs: "flex-start", sm: "center" },
                            gap: { xs: 1, sm: 2 },
                          }}
                        >
                          <Typography
                            variant="subtitle1"
                            sx={{
                              color: "text.primary",
                              fontWeight: 600,
                              fontSize: { xs: "0.875rem", sm: "1rem" },
                            }}
                          >
                            {student.username}
                          </Typography>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                            }}
                          >
                            <SchoolIcon
                              sx={{
                                fontSize: 16,
                                color: theme.palette.secondary.main,
                              }}
                            />
                            <Typography
                              variant="body2"
                              sx={{
                                color: "text.secondary",
                                fontSize: { xs: "0.75rem", sm: "0.875rem" },
                              }}
                            >
                              {courseCode}
                            </Typography>
                          </Box>
                          {(student.level !== undefined ||
                            student.exp !== undefined) && (
                            <Box
                              sx={{
                                display: "flex",
                                gap: 2,
                                flexWrap: "wrap",
                                mt: { xs: 1, sm: 0 },
                              }}
                            >
                              {student.level !== undefined && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: theme.palette.primary.main,
                                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  }}
                                >
                                  Level: {student.level}
                                </Typography>
                              )}
                              {student.exp !== undefined && (
                                <Typography
                                  variant="body2"
                                  sx={{
                                    color: theme.palette.secondary.main,
                                    fontSize: { xs: "0.75rem", sm: "0.875rem" },
                                  }}
                                >
                                  EXP: {student.exp}
                                </Typography>
                              )}
                            </Box>
                          )}
                        </Box>
                      }
                      secondary={
                        <Typography
                          variant="body2"
                          sx={{ color: "text.secondary", mt: 0.5 }}
                        >
                          {student.first_name} {student.last_name} •{" "}
                          {student.email}
                        </Typography>
                      }
                    />

                    <ListItemSecondaryAction
                      sx={{
                        position: { xs: "relative", sm: "absolute" },
                        transform: { xs: "none", sm: "translateY(-50%)" },
                        top: { xs: "auto", sm: "50%" },
                        right: { xs: 0, sm: 16 },
                        mt: { xs: 2, sm: 0 },
                        width: { xs: "100%", sm: "auto" },
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: { xs: "center", sm: "flex-end" },
                          width: "100%",
                        }}
                      >
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<UnregisterIcon />}
                          onClick={() => handleUnregister(student.id)}
                          sx={{
                            width: { xs: "auto", sm: "auto" },
                            minWidth: { xs: 90, sm: 90 },
                            borderColor: theme.palette.error.main,
                            color: theme.palette.error.main,
                            "&:hover": {
                              borderColor: theme.palette.error.dark,
                              backgroundColor: "rgba(244, 67, 54, 0.1)",
                            },
                            fontSize: { xs: "0.75rem", sm: "0.875rem" },
                            py: { xs: 1, sm: "auto" },
                          }}
                        >
                          Unregister
                        </Button>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < enrolledStudents.length - 1 && (
                    <Divider sx={{ bgcolor: "rgba(100, 255, 218, 0.1)" }} />
                  )}
                </React.Fragment>
              ))}
            </List>
          </Paper>
        )}
      </Box>
    </Box>
  );
};

export default CourseRegistrations;
