import React from "react";
import { Box, Typography, Modal, Avatar } from "@mui/material";

export interface EvaluatorInfo {
  id: number;
  profile_pic?: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
}

interface EvaluatorInfoModalProps {
  open: boolean;
  onClose: () => void;
  evaluator: EvaluatorInfo | null;
}

const EvaluatorInfoModal: React.FC<EvaluatorInfoModalProps> = ({
  open,
  onClose,
  evaluator,
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="evaluator-info-modal"
      aria-describedby="evaluator-info-modal-description"
    >
      <Box
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width:  320,
          bgcolor: "background.paper",
          border: "2px solid #000",
          boxShadow: 24,
          p: 4,
          borderRadius: 1,
        }}
      >
        <Typography id="evaluator-info-modal" variant="h6" component="h2" align="center">
          Evaluator Information
        </Typography>
        {evaluator ? (
          <Box>
            <Avatar
              sx={{ width: 100, height: 100, mx: "auto", my: 2 }}
              src={evaluator.profile_pic}
            />
            <Typography sx={{overflowWrap: "anywhere"}}>
              Name: {evaluator.first_name} {evaluator.last_name}
            </Typography>
            <Typography sx={{overflowWrap: "anywhere"}}>Email: {evaluator.email}</Typography>
            {evaluator.phone_number && evaluator.phone_number.length > 0 && (
              <Typography sx={{overflowWrap: "break-word"}}>Phone: {evaluator.phone_number}</Typography>
            )}
          </Box>
        ) : (
          <Typography>No evaluator information available.</Typography>
        )}
      </Box>
    </Modal>
  );
};

export default EvaluatorInfoModal;
