import React, { useState, useEffect } from "react";
import {
  Box,
  Grid2 as Grid,
  Paper,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  useTheme,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar,
  CardActions,
  ButtonGroup,
  Divider,
  Tooltip,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import PeopleIcon from "@mui/icons-material/People";
import AssignmentIcon from "@mui/icons-material/Assignment";
import ShareIcon from "@mui/icons-material/Share";
import { useNavigate } from "react-router-dom";
import api from "@/services/api";

// Classes are courses in the API, so we use the term "class" for consistency in the UI.
interface Class {
  id: string;
  name: string;
  studentsCount: number;
  description: string;
  status: "active" | "draft";
  lastActive: string;
  evaluationsCompleted: number;
  evaluationsPending: number;
  progressPercent: number;
  pendingRegistrationsCount: number;
}

interface ShareDialogState {
  open: boolean;
  classId: string | null;
  className: string;
  link: string;
}

const EducatorDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [classes, setClasses] = useState<Class[]>([]);
  const [shareDialog, setShareDialog] = useState<ShareDialogState>({
    open: false,
    classId: null,
    className: "",
    link: "",
  });
  const [customMessage, setCustomMessage] = useState("");
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const fetchClasses = async () => {
    try {
      setLoading(true);
      const response = await api.get("/projects/courses/");

      // Transform the API response to match our interface
      const transformedClasses = response.data.map((course: any) => ({
        id: course.code, // Using code as ID since it's the primary key
        name: course.name,
        description: course.description,
        studentsCount: course.student_count || 0,
        status: course.status || "draft", // Default to draft if no status is provided
        lastActive:
          course.last_active || new Date().toISOString().split("T")[0],
        evaluationsCompleted: course.evaluations_completed || 0,
        evaluationsPending: course.evaluations_pending || 0,
        progressPercent: course.progress_percent || 0,
        pendingRegistrationsCount: course.pending_registrations_count || 0,
      }));

      setClasses(transformedClasses);
      setError(null);
    } catch (err: any) {
      console.error("Error fetching classes:", err);
      setError(
        err.response?.data?.detail ||
          "Error fetching classes. Please try again later."
      );
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  const handleShare = (classId: string) => {
    const classItem = classes.find((c) => c.id === classId);
    if (classItem) {
      // Generate a shareable link - replace with your actual domain
      const shareableLink = `${window.location.origin}/join-class/${classId}`;
      setShareDialog({
        open: true,
        classId,
        className: classItem.name,
        link: shareableLink,
      });
      setCustomMessage(
        `Join my class "${classItem.name}" on DevSpace! Click here to join: ${shareableLink}`
      );
    }
  };

  const handleCloseShare = () => {
    setShareDialog({
      open: false,
      classId: null,
      className: "",
      link: "",
    });
    setCustomMessage("");
  };

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(customMessage);
      setSnackbarOpen(true);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  const handleViewStatistics = (classId: string) => {
    navigate(`/course/${classId}/statistics`);
  };

  if (loading) {
    return (
      <Box sx={{ textAlign: "center", mt: 5 }}>
        <Typography variant="h6" sx={{ color: "text.primary" }}>
          Loading classes...
        </Typography>
      </Box>
    );
  }
  if (error) {
    return (
      <Box sx={{ textAlign: "center", mt: 5 }}>
        <Typography variant="h6" sx={{ color: "error.main" }}>
          {error}
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box
        sx={{
          mb: 4,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          flexDirection: { xs: "column", sm: "row" },
          gap: { xs: 2, sm: 0 },
        }}
      >
        <Typography
          variant="h4"
          sx={{ color: "text.primary", fontWeight: 600 }}
        >
          Educator Dashboard
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate("/create-class")}
            sx={{
              background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
              color: "#0A192F",
              "&:hover": {
                background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
              },
              width: { xs: "100%", sm: "auto" },
            }}
          >
            New Course
          </Button>
        </Box>
      </Box>

      {/* Stats Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Paper
            sx={{
              p: 3,
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <AssignmentIcon
                sx={{ color: theme.palette.primary.main, mr: 1 }}
              />
              <Typography variant="h6" sx={{ color: "text.primary" }}>
                Active Classes
              </Typography>
            </Box>
            <Typography variant="h3" sx={{ color: theme.palette.primary.main }}>
              {classes.filter((c) => c.status === "active").length}
            </Typography>
          </Paper>
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }}>
          <Paper
            sx={{
              p: 3,
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
              <PeopleIcon sx={{ color: theme.palette.secondary.main, mr: 1 }} />
              <Typography variant="h6" sx={{ color: "text.primary" }}>
                Total Students
              </Typography>
            </Box>
            <Typography
              variant="h3"
              sx={{ color: theme.palette.secondary.main }}
            >
              {classes.reduce((acc, curr) => acc + curr.studentsCount, 0)}
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Classes Grid */}
      <Typography variant="h5" sx={{ mb: 3, color: "text.primary" }}>
        Your Classes
      </Typography>
      <Grid container spacing={3}>
        {classes.map((classItem) => (
          <Grid size={{ xs: 12, md: 6 }} key={classItem.id}>
            <Card
              sx={{
                height: "100%",
                display: "flex",
                flexDirection: "column",
                background: "rgba(17, 34, 64, 0.95)",
                backdropFilter: "blur(10px)",
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
                transition: "transform 0.2s, box-shadow 0.2s",
                "&:hover": {
                  transform: "translateY(-2px)",
                  boxShadow: `0 8px 25px rgba(100, 255, 218, 0.1)`,
                },
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "flex-start",
                    mb: 2,
                  }}
                >
                  <Typography variant="h6" sx={{ flex: 1 }}>
                    {classItem.name}
                  </Typography>
                  <Button
                    size="small"
                    onClick={() => handleViewStatistics(classItem.id)}
                    startIcon={<BarChartIcon />}
                    sx={{
                      color: theme.palette.primary.main,
                      minWidth: "auto",
                      p: 1,
                      "&:hover": {
                        backgroundColor: "rgba(100, 255, 218, 0.1)",
                      },
                    }}
                  >
                    Statistics
                  </Button>
                </Box>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                    overflow: "hidden",
                  }}
                >
                  {classItem.description}
                </Typography>

                <Box sx={{ mt: 2 }}>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <PeopleIcon
                      sx={{ fontSize: 20, mr: 1, color: "text.secondary" }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      {classItem.studentsCount} Students
                    </Typography>
                  </Box>

                  {classItem.pendingRegistrationsCount > 0 && (
                    <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                      <PendingIcon
                        sx={{ fontSize: 20, mr: 1, color: "warning.main" }}
                      />
                      <Typography variant="body2" color="warning.main">
                        {classItem.pendingRegistrationsCount} Pending
                        Registration
                        {classItem.pendingRegistrationsCount !== 1 ? "s" : ""}
                      </Typography>
                    </Box>
                  )}

                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <AccessTimeIcon
                      sx={{ fontSize: 20, mr: 1, color: "text.secondary" }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      Last active{" "}
                      {formatDistanceToNow(new Date(classItem.lastActive))} ago
                    </Typography>
                  </Box>
                </Box>
              </CardContent>

              <Divider />

              <CardActions
                sx={{
                  p: 2,
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <ButtonGroup
                  size="small"
                  sx={{
                    "& .MuiButton-root": {
                      minWidth: { xs: "40px", sm: "auto" },
                      px: { xs: 1, sm: 2 },
                    },
                  }}
                >
                  <Tooltip title="Registrations">
                    <Button
                      onClick={() =>
                        navigate(`/course/${classItem.id}/registrations`)
                      }
                    >
                      <PeopleIcon />
                      <Box
                        sx={{
                          display: { xs: "none", sm: "inline" },
                          ml: { sm: 1 },
                        }}
                      >
                        Registrations
                      </Box>
                    </Button>
                  </Tooltip>
                  <Tooltip title="Manage">
                    <Button
                      onClick={() => navigate(`/edit-class/${classItem.id}`)}
                    >
                      <SettingsIcon />
                      <Box
                        sx={{
                          display: { xs: "none", sm: "inline" },
                          ml: { sm: 1 },
                        }}
                      >
                        Manage
                      </Box>
                    </Button>
                  </Tooltip>
                  <Tooltip title="Share">
                    <Button onClick={() => handleShare(classItem.id)}>
                      <ShareIcon />
                      <Box
                        sx={{
                          display: { xs: "none", sm: "inline" },
                          ml: { sm: 1 },
                        }}
                      >
                        Share
                      </Box>
                    </Button>
                  </Tooltip>
                </ButtonGroup>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Share Dialog */}
      <Dialog
        open={shareDialog.open}
        onClose={handleCloseShare}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            background: "rgba(17, 34, 64, 0.95)",
            backdropFilter: "blur(10px)",
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
          },
        }}
      >
        <DialogTitle sx={{ color: "text.primary" }}>
          Share {shareDialog.className}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              sx={{ mb: 1, color: "text.secondary" }}
            >
              Shareable Link
            </Typography>
            <TextField
              fullWidth
              value={shareDialog.link}
              slotProps={{
                input: {
                  readOnly: true,
                  sx: { bgcolor: "rgba(255, 255, 255, 0.05)" },
                },
              }}
              sx={{ mb: 2 }}
            />
            <Typography
              variant="subtitle2"
              sx={{ mb: 1, color: "text.secondary" }}
            >
              Customize Message
            </Typography>
            <TextField
              fullWidth
              multiline
              rows={4}
              value={customMessage}
              onChange={(
                e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
              ) => setCustomMessage(e.target.value)}
              placeholder="Add a personal message..."
              sx={{
                "& .MuiInputBase-root": {
                  bgcolor: "rgba(255, 255, 255, 0.05)",
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button onClick={handleCloseShare} sx={{ color: "text.secondary" }}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleCopyToClipboard}
            sx={{
              background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
              color: "#0A192F",
              "&:hover": {
                background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
              },
            }}
          >
            Copy to Clipboard
          </Button>
        </DialogActions>
      </Dialog>

      {/* Copy Success Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message="Message copied to clipboard!"
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      />
    </Box>
  );
};

export default EducatorDashboard;
