import React, { useState, useEffect } from "react";
import {
  <PERSON>ge,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  Button,
  useTheme,
  Chip,
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  PendingActions as PendingIcon,
  SignalWifiOff as DisconnectedIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import api from '../../services/api';
import { RegistrationRequest } from '../RegistrationRequestCard';
import { useSSENotifications } from '../../hooks/useSSENotifications';

const RegistrationNotificationBadge: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  // Use SSE for real-time notifications
  const {
    pendingCount,
    pendingRequests,
    isConnected,
    error: sseError,
    reconnect
  } = useSSENotifications();

  // Fallback to polling if SSE fails
  const [fallbackRequests, setFallbackRequests] = useState<RegistrationRequest[]>([]);
  const [useFallback, setUseFallback] = useState(false);

  const fetchPendingRequests = async () => {
    try {
      const response = await api.get('/projects/courses/registrations/pending/');
      setFallbackRequests(response.data);
    } catch (error) {
      console.error("Failed to fetch pending requests:", error);
    }
  };

  // Enable fallback polling if SSE is not connected and has error
  useEffect(() => {
    if (!isConnected && sseError) {
      console.log('SSE failed, enabling fallback polling mode');
      setUseFallback(true);
      fetchPendingRequests();

      const interval = setInterval(fetchPendingRequests, 30000); // 30 second fallback polling
      return () => clearInterval(interval);
    } else if (isConnected && !sseError) {
      console.log('SSE connected successfully, disabling fallback mode');
      setUseFallback(false);
    }
  }, [isConnected, sseError]);

  // Use SSE data if available, otherwise fallback data
  const currentRequests = useFallback ? fallbackRequests : pendingRequests;
  const currentCount = useFallback ? fallbackRequests.length : pendingCount;

  // Debug logging
  useEffect(() => {
    console.log('Notification Badge State:', {
      isConnected,
      sseError,
      useFallback,
      currentCount,
      pendingCount,
      fallbackRequestsLength: fallbackRequests.length
    });
  }, [isConnected, sseError, useFallback, currentCount, pendingCount, fallbackRequests.length]);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleViewCourseRegistrations = (courseCode: string) => {
    navigate(`/course/${courseCode}/registrations`);
    handleClose();
  };

  const handleViewAllRegistrations = () => {
    // For now, navigate to educator dashboard where the RegistrationManagement component is
    navigate("/educator-dashboard");
    handleClose();
  };

  const open = Boolean(anchorEl);

  // Group requests by course for better display
  const requestsByCourse = currentRequests.reduce((acc, request) => {
    const courseCode = request.course.code;
    if (!acc[courseCode]) {
      acc[courseCode] = {
        course: request.course,
        requests: [],
      };
    }
    acc[courseCode].requests.push(request);
    return acc;
  }, {} as Record<string, { course: RegistrationRequest['course']; requests: RegistrationRequest[] }>);

  return (
    <>
      <Tooltip title={
        useFallback
          ? `${currentCount} pending registration${currentCount !== 1 ? 's' : ''} (Fallback mode)`
          : !isConnected && sseError
            ? `Connection error: ${sseError}`
            : isConnected
              ? `${currentCount} pending registration${currentCount !== 1 ? 's' : ''} (Live updates)`
              : `${currentCount} pending registration${currentCount !== 1 ? 's' : ''} (Connecting...)`
      }>
        <IconButton
          onClick={handleClick}
          sx={{
            color: currentCount > 0 ? theme.palette.warning.main : 'text.secondary',
            position: 'relative',
          }}
        >
          <Badge
            badgeContent={currentCount}
            color="warning"
            max={99}
            showZero={true}
            sx={{
              "& .MuiBadge-badge": {
                backgroundColor: theme.palette.warning.main,
                color: theme.palette.warning.contrastText,
              },
            }}
          >
            {!isConnected && sseError && !useFallback ? <DisconnectedIcon /> : <NotificationsIcon />}
          </Badge>

          {/* Connection status indicator - always show for educators */}
          <Box
            sx={{
              position: 'absolute',
              top: 2,
              right: 2,
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: useFallback
                ? 'warning.main'  // Orange for fallback mode
                : isConnected
                  ? 'success.main'  // Green for connected
                  : 'error.main',   // Red for disconnected
              border: '1px solid',
              borderColor: 'background.paper',
            }}
          />
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 350,
            maxHeight: 400,
            background: "rgba(17, 34, 64, 0.95)",
            backdropFilter: "blur(10px)",
            border: `1px solid ${theme.palette.divider}`,
          },
        }}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
      >
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="h6" sx={{ color: 'text.primary' }}>
              Registration Requests
            </Typography>

            {/* Connection status and reconnect button */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {useFallback ? (
                <Chip
                  label="Fallback Mode"
                  size="small"
                  color="warning"
                  sx={{ fontSize: '0.7rem' }}
                />
              ) : (
                <Chip
                  label={isConnected ? "Live" : "Disconnected"}
                  size="small"
                  color={isConnected ? "success" : "error"}
                  sx={{ fontSize: '0.7rem' }}
                />
              )}

              {(!isConnected || useFallback) && (
                <IconButton
                  size="small"
                  onClick={reconnect}
                  sx={{ color: 'text.secondary' }}
                >
                  <RefreshIcon fontSize="small" />
                </IconButton>
              )}
            </Box>
          </Box>

          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {currentCount} student{currentCount !== 1 ? 's' : ''} waiting for approval
          </Typography>
        </Box>

        <Divider />

        {currentCount === 0 ? (
          <MenuItem sx={{ p: 0 }}>
            <Box sx={{ p: 3, textAlign: 'center', width: '100%' }}>
              <PendingIcon sx={{ fontSize: 48, color: 'text.secondary', opacity: 0.3, mb: 1 }} />
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                No pending requests
              </Typography>
            </Box>
          </MenuItem>
        ) : (
          [
            <MenuItem key="requests" sx={{ display: "block", p: 0 }}>
              <Box sx={{ maxHeight: 250, overflowY: "auto" }}>
                {Object.entries(requestsByCourse).map(
                  ([courseCode, { course, requests }]) => (
                    <Box
                      key={courseCode}
                      onClick={() => handleViewCourseRegistrations(courseCode)}
                      sx={{
                        p: 1.5,
                        cursor: "pointer",
                        "&:hover": {
                          backgroundColor: "rgba(123, 137, 244, 0.1)",
                        },
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          width: "100%",
                          mb: 0.5,
                        }}
                      >
                        <PendingIcon
                          sx={{
                            fontSize: 18,
                            color: theme.palette.warning.main,
                            mr: 1,
                          }}
                        />
                        <Typography
                          variant="subtitle2"
                          sx={{ color: "text.primary", flex: 1 }}
                        >
                          {course.name}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{ color: "text.secondary" }}
                        >
                          {requests.length} pending
                        </Typography>
                      </Box>
                      <Typography
                        variant="caption"
                        sx={{ color: "text.secondary", ml: 3 }}
                      >
                        {course.code} •{" "}
                        {requests.map((r) => r.student_name).join(", ")}
                      </Typography>
                    </Box>
                  )
                )}
              </Box>
            </MenuItem>,
            <MenuItem key="divider" sx={{ p: 0 }}>
              <Divider sx={{ width: "100%" }} />
            </MenuItem>,
            <MenuItem key="viewAll" sx={{ p: 0 }}>
              <Box sx={{ p: 2, width: "100%" }}>
                <Button
                  fullWidth
                  variant="outlined"
                  size="small"
                  onClick={handleViewAllRegistrations}
                  sx={{
                    borderColor: theme.palette.primary.main,
                    color: theme.palette.primary.main,
                    "&:hover": {
                      borderColor: theme.palette.primary.dark,
                      backgroundColor: "rgba(100, 255, 218, 0.1)",
                    },
                  }}
                >
                  View All Requests
                </Button>
              </Box>
            </MenuItem>,
          ]
        )}
      </Menu>
    </>
  );
};

export default RegistrationNotificationBadge;
