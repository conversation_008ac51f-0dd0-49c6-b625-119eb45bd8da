import React, { useState } from "react";
import { useN<PERSON><PERSON>, Link, useLocation } from "react-router-dom";
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Alert,
  MenuItem,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import BackToHome from "@/components/BackToHome";
import api from "@/services/api";

import { useAuth } from "@/contexts/AuthContext";

type FormData = {
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  password: string;
  password_confirm: string;
  role: string;
};

type LocationState = {
  role?: string;
};

const Register = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setUserAndToken } = useAuth();
  
  // Get role from location state if available
  const locationState = location.state as LocationState;
  const defaultRole = locationState?.role || "student";
  
  const [formData, setFormData] = useState<FormData | null>({
    email: "",
    first_name: "",
    last_name: "",
    phone_number: "+971",
    password: "",
    password_confirm: "",
    role: defaultRole,
  });
  
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const validatePhoneNumber = (phone: string): boolean => {
    const phoneRegex = /^\+971\d{9}$/;
    return phoneRegex.test(phone);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    if (name === 'phone_number') {
      // Only allow digits and limit to 9 digits after +971
      if (/^\d{0,9}$/.test(value)) {
        setFormData({
          ...formData,
          phone_number: `+971${value}`
        } as FormData);
      }
      return;
    }

    // Handle email with max length of 50 characters
    if (name === 'email') {
      if (value.length <= 50) {
        setFormData({
          ...formData,
          [name]: value.toLowerCase().trim(),
        } as FormData);
      }
      return;
    }

    // Handle password and password_confirm with max length of 30 characters
    if (name === 'password' || name === 'password_confirm') {
      if (value.length <= 30) {
        setFormData({
          ...formData,
          [name]: value,
        } as FormData);
      }
      return;
    }

    // Handle first_name and last_name with max length of 15 characters
    if (name === 'first_name' || name === 'last_name') {
      if (value.length <= 15) {
        setFormData({
          ...formData,
          [name]: value.trim(),
        } as FormData);
      }
      return;
    }

    // For other fields (role)
    setFormData({
      ...formData,
      [name]: value,
    } as FormData);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Validate fields
    if (
      !formData ||
      !formData.email ||
      !formData.password ||
      !formData.password_confirm ||
      !formData.first_name ||
      !formData.last_name ||
      !formData.phone_number
    ) {
      setError("Please fill in all fields");
      return;
    }

    // Validate field lengths
    if (formData.email.length > 50) {
      setError("Email must not exceed 50 characters");
      return;
    }

    if (formData.first_name.length > 15) {
      setError("First name must not exceed 15 characters");
      return;
    }

    if (formData.last_name.length > 15) {
      setError("Last name must not exceed 15 characters");
      return;
    }

    if (formData.password.length > 30) {
      setError("Password must not exceed 30 characters");
      return;
    }

    if (formData.password_confirm.length > 30) {
      setError("Password confirmation must not exceed 30 characters");
      return;
    }

    // Validate phone number
    if (!validatePhoneNumber(formData.phone_number)) {
      setError('Phone number must start with +971 and be followed by 9 digits.');
      return;
    }

    // Validate first name and last name format
    const nameRegex = /^[a-zA-Z\s-']+$/;
    if (!nameRegex.test(formData.first_name)) {
      setError("First name can only contain letters, spaces, hyphens, and apostrophes");
      return;
    }
    if (!nameRegex.test(formData.last_name)) {
      setError("Last name can only contain letters, spaces, hyphens, and apostrophes");
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError("Please enter a valid email address");
      return;
    }

    // Validate password
    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    // Check if password is entirely numeric
    if (/^\d+$/.test(formData.password)) {
      setError("Password cannot be entirely numeric");
      return;
    }

    // Check for uppercase, lowercase, and number
    if (!/[A-Z]/.test(formData.password)) {
      setError("Password must contain at least one uppercase letter");
      return;
    }
    if (!/[a-z]/.test(formData.password)) {
      setError("Password must contain at least one lowercase letter");
      return;
    }
    if (!/\d/.test(formData.password)) {
      setError("Password must contain at least one number");
      return;
    }

    // Check for common passwords
    const commonPasswords = ['password', 'password123', '12345678', 'qwerty123', 'admin123'];
    if (commonPasswords.includes(formData.password.toLowerCase())) {
      setError("This password is too common");
      return;
    }

    // Check if password contains personal information
    const personalInfo = [formData.email, formData.first_name, formData.last_name];
    for (const info of personalInfo) {
      if (info && info.length > 2 && formData.password.toLowerCase().includes(info.toLowerCase())) {
        setError("Password cannot contain your personal information");
        return;
      }
    }

    if (formData.password !== formData.password_confirm) {
      setError("Passwords do not match");
      return;
    }

    try {
      // Map 'role' to 'user_type' before sending
      const { role, ...rest } = formData;
      const requestData = {
        ...rest,
        user_type: role === 'student' ? 'S' : 'E',  // 'S' for student, 'E' for teacher
      };

      const response = await api.post("/users/register/", requestData);
      setUserAndToken(response.data.user, response.data.token);
      navigate("/approval", {
        state: {
          message: "Registration successful! Please wait for admin approval.",
        },
      });
    } catch (err: any) {
      if (err.response?.status === 400) {
        // Handle specific validation errors from backend
        const errors = err.response.data;
        if (errors.email) {
          setError(`Email: ${errors.email[0]}`);
        } else if (errors.firstName) {
          setError(`First Name: ${errors.firstName[0]}`);
        } else if (errors.lastName) {
          setError(`Last Name: ${errors.lastName[0]}`);
        } else if (errors.password) {
          setError(`Password: ${errors.password[0]}`);
        } else {
          setError("Registration failed");
        }
      } else {
        setError("An error occurred. Please try again.");
      }
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <BackToHome />
      <Box
        sx={{
          marginTop: 8,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: "100%" }}>
          <Typography component="h1" variant="h5" align="center" gutterBottom>
            Create Account
          </Typography>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email Address"
              name="email"
              autoComplete="email"
              value={formData?.email || ""}
              onChange={handleChange}
              inputProps={{ maxLength: 50 }}
            />
            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="first_name"
                label="First Name"
                name="first_name"
                autoComplete="given-name"
                value={formData?.first_name || ""}
                onChange={handleChange}
                inputProps={{ maxLength: 15 }}
              />
              <TextField
                margin="normal"
                required
                fullWidth
                id="last_name"
                label="Last Name"
                name="last_name"
                autoComplete="family-name"
                value={formData?.last_name || ""}
                onChange={handleChange}
                inputProps={{ maxLength: 15 }}
              />
            </Box>
            <TextField
              select
              margin="normal"
              required
              fullWidth
              id="role"
              label="Role"
              name="role"
              value={formData?.role || "student"}
              onChange={handleChange}
            >
              <MenuItem value="student">Student</MenuItem>
              <MenuItem value="teacher">Teacher</MenuItem>
            </TextField>
            <TextField
              margin="normal"
              required
              fullWidth
              name="phone_number"
              label="Phone Number"
              type="text"
              id="phone_number"
              autoComplete="tel"
              onChange={handleChange}
              value={(formData?.phone_number || '').replace(/^\+971/, '')}
              placeholder="123456789"
              InputProps={{
                startAdornment: (
                  <Typography sx={{ mr: 1 }}>+971</Typography>
                ),
              }}

            />
            <TextField
              fullWidth
              label="Password"
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={formData?.password || ""}
              onChange={handleChange}
              margin="normal"
              required
              inputProps={{ maxLength: 30 }}

              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <TextField
              fullWidth
              label="Confirm Password"
              id="password_confirm"
              name="password_confirm"
              type={showConfirmPassword ? "text" : "password"}
              value={formData?.password_confirm || ""}
              onChange={handleChange}
              margin="normal"
              required
              inputProps={{ maxLength: 30 }}

              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
            >
              Register
            </Button>
            <Box sx={{ textAlign: "center", mt: 2 }}>
              <Link
                to="/login"
                style={{ textDecoration: "none", color: "inherit" }}
              >
                <Typography variant="body2" color="primary">
                  Already have an account? Sign in
                </Typography>
              </Link>
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default Register;
