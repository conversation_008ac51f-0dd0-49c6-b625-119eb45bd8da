#!/bin/bash

# Check if DEBUG is set to True
if [ "$DEV_ENV" = "development" ]; then
    echo "Running in development mode..."
    ln -sf /etc/nginx/sites-available/development.conf /etc/nginx/sites-enabled/default
elif [ "$DEV_ENV" = "staging" ]; then
    echo "Running in staging mode..."
    ln -sf /etc/nginx/sites-available/api-s.42devspace.tech /etc/nginx/sites-enabled/default

    # Check if Cloudflare certificates exist
    if [ ! -f "/etc/nginx/ssl/cloudflare/cert.pem" ] || [ ! -f "/etc/nginx/ssl/cloudflare/key.pem" ]; then
        echo "Warning: Cloudflare certificates not found in /etc/nginx/ssl/cloudflare/"
        echo "Please ensure you have placed your Cloudflare Origin certificates in /etc/nginx/ssl/cloudflare/cert.pem and /etc/nginx/ssl/cloudflare/key.pem"
        exit 1
    fi
    chmod 600 /etc/nginx/ssl/cloudflare/key.pem
    chmod 644 /etc/nginx/ssl/cloudflare/cert.pem
else
    echo "Running in production mode..."
    ln -sf /etc/nginx/sites-available/api.42devspace.tech /etc/nginx/sites-enabled/default
    
    # Check if Cloudflare certificates exist
    if [ ! -f "/etc/nginx/ssl/cloudflare/cert.pem" ] || [ ! -f "/etc/nginx/ssl/cloudflare/key.pem" ]; then
        echo "Warning: Cloudflare certificates not found in /etc/nginx/ssl/cloudflare/"
        echo "Please ensure you have placed your Cloudflare Origin certificates in /etc/nginx/ssl/cloudflare/cert.pem and /etc/nginx/ssl/cloudflare/key.pem"
        exit 1
    fi
    chmod 600 /etc/nginx/ssl/cloudflare/key.pem
    chmod 644 /etc/nginx/ssl/cloudflare/cert.pem
fi



# Start nginx
exec nginx -g 'daemon off;'
