import logging
from .gemini_service import GeminiService
from .pdf_processor import PDFProcessor

logger = logging.getLogger(__name__)

class QuestionGenerator:
    """Main service for generating evaluation questions from project PDFs"""
    
    def __init__(self):
        self.pdf_processor = PDFProcessor()
        self.gemini_service = GeminiService()
    
    def generate_questions_for_project(self, project, num_questions=5):
        """Generate evaluation questions based on project PDF content"""
        
        if not project.pdf_file:
            raise ValueError("Project must have a PDF file to generate questions")
        
        try:
            pdf_text = self.pdf_processor.extract_text_from_pdf(project.pdf_file.path)
            
        except Exception as e:
            logger.error(f"Failed to extract text from PDF: {e}")
            raise ValueError(f"Could not process PDF file: {str(e)}")
        
        try:
            course_track = project.course.track.name if project.course.track else "General"
            
            questions_data = self.gemini_service.generate_evaluation_questions(
                project_content=pdf_text,
                project_title=project.title,
                project_description=project.description,
                course_track=course_track,
                num_questions=num_questions
            )
            
            questions_data['metadata'] = {
                'project_id': project.id,
                'project_title': project.title,
                'course_track': course_track,
                'pdf_text_length': len(pdf_text),
                'generation_params': {
                    'num_questions': num_questions
                }
            }
            
            return questions_data
            
        except Exception as e:
            logger.error(f"Failed to generate questions: {e}")
            raise ValueError(f"Could not generate questions: {str(e)}")

    def generate_questions_from_pdf_file(self, pdf_path, project_title, project_description, num_questions=5):
        """Generate evaluation questions from a PDF file directly"""
        
        try:
            pdf_text = self.pdf_processor.extract_text_from_pdf(pdf_path)
            
        except Exception as e:
            logger.error(f"Failed to extract text from PDF: {e}")
            raise ValueError(f"Could not process PDF file: {str(e)}")
        
        try:
            questions_data = self.gemini_service.generate_evaluation_questions(
                project_content=pdf_text,
                project_title=project_title,
                project_description=project_description,
                course_track="General",
                num_questions=num_questions
            )
            
            questions_data['metadata'] = {
                'project_title': project_title,
                'pdf_text_length': len(pdf_text),
                'generation_params': {
                    'num_questions': num_questions
                }
            }
            
            return questions_data
            
        except Exception as e:
            logger.error(f"Failed to generate questions: {e}")
            raise ValueError(f"Could not generate questions: {str(e)}")

    def refine_questions_from_pdf_file(self, pdf_path, project_title, project_description, num_questions=5, user_instructions="", previous_questions=None):
        """Refine evaluation questions from a PDF file based on user feedback"""
        
        try:
            pdf_text = self.pdf_processor.extract_text_from_pdf(pdf_path)
            
        except Exception as e:
            logger.error(f"Failed to extract text from PDF: {e}")
            raise ValueError(f"Could not process PDF file: {str(e)}")
        
        try:
            questions_data = self.gemini_service.refine_evaluation_questions(
                project_content=pdf_text,
                project_title=project_title,
                project_description=project_description,
                course_track="General",
                num_questions=num_questions,
                user_instructions=user_instructions,
                previous_questions=previous_questions or []
            )
            
            questions_data['metadata'] = {
                'project_title': project_title,
                'pdf_text_length': len(pdf_text),
                'generation_params': {
                    'num_questions': num_questions,
                    'user_instructions': user_instructions,
                    'refined_from_previous': len(previous_questions or [])
                }
            }
            
            return questions_data
            
        except Exception as e:
            logger.error(f"Failed to refine questions: {e}")
            raise ValueError(f"Could not refine questions: {str(e)}")