import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "../DashboardLayout";

interface EducatorGuardProps {
  children: React.ReactNode;
}

const EducatorGuard: React.FC<EducatorGuardProps> = ({ children }) => {
  const { user } = useAuth();
  const location = useLocation();

  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user is approved and email verified
  if (
    (!user.is_approved || !user.is_email_verified) &&
    location.pathname !== "/approval"
  ) {
    return <Navigate to="/approval" replace />;
  }

  // Check if user is an educator
  if (user.user_type !== "E") {
    return <Navigate to="/dashboard" replace />;
  }

  return <DashboardLayout>{children}</DashboardLayout>;
};

export default EducatorGuard;
