import React, { use<PERSON>emo, use<PERSON><PERSON>back, useState } from "react";
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  IconButton,
  useTheme,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  <PERSON>lider,
  Modal,
  CircularProgress,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import AddIcon from "@mui/icons-material/Add";
import PercentIcon from "@mui/icons-material/Percent";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import AutoAwesomeIcon from "@mui/icons-material/AutoAwesome";
import { courseService } from "@/services/courseService";


export interface Question {
  id: string;
  text: string;
  type: "binary";
  weight: number;
}

export interface PDFEvaluation {
  pdfId: string;
  questions: Question[];
  totalWeight: number;
}

export interface EvaluationSheet {
  pdfEvaluations: PDFEvaluation[];
  totalWeight: number;
  errors?: {
    weightValidation?: string;
    [key: string]: string | undefined;
  };
}

interface EvaluationSheetManagementProps {
  evaluationSheet: EvaluationSheet;
  pdfs: Array<{ id: string; title: string; file: File; description: string }>;
  onEvaluationSheetChange: (sheet: EvaluationSheet) => void;
  errors: { [key: string]: string };
}

const WEIGHT_MARGIN = 0.1;

const EvaluationSheetManagement: React.FC<EvaluationSheetManagementProps> = ({
  evaluationSheet,
  pdfs,
  onEvaluationSheetChange,
  errors,
}) => {
  const theme = useTheme();
  const [openModal, setOpenModal] = useState(false);
  const [selectedPdfId, setSelectedPdfId] = useState<string | null>(null);
  const [tempWeights, setTempWeights] = useState<{ [key: string]: number }>({});
  const [loadingAI, setLoadingAI] = useState<{[key: string]: boolean}>({});
  const [numQuestionsInput, setNumQuestionsInput] = useState<{[key: string]: number}>({});
  const [showRefinement, setShowRefinement] = useState<{[key: string]: boolean}>({});
  const [refinementInstructions, setRefinementInstructions] = useState<{[key: string]: string}>({});
  const [previousQuestions, setPreviousQuestions] = useState<{[key: string]: any[]}>({});

  // Efficiently distribute weights for a single PDF evaluation
  const distributeWeights = useCallback(
    (
      questions: Question[],
      changedQuestionId?: string,
      newWeight?: number
    ): Question[] => {
      const totalQuestions = questions.length;
      if (totalQuestions === 0) return [];
      if (totalQuestions === 1)
        return questions.map((q) => ({ ...q, weight: 100 }));

      let updatedQuestions = [...questions];

      if (changedQuestionId && newWeight !== undefined) {
        // Handle weight change for a specific question
        const otherQuestions = questions.filter(
          (q) => q.id !== changedQuestionId
        );
        const remainingWeight = 100 - newWeight;
        const oldTotal = otherQuestions.reduce((sum, q) => sum + q.weight, 0);

        updatedQuestions = questions.map((q) => {
          if (q.id === changedQuestionId) return { ...q, weight: newWeight };
          // Distribute remaining weight proportionally
          const adjustedWeight =
            oldTotal > 0
              ? (q.weight / oldTotal) * remainingWeight
              : remainingWeight / otherQuestions.length;
          return { ...q, weight: Math.round(adjustedWeight * 10) / 10 };
        });
      } else {
        // Distribute weights evenly
        const baseWeight = Math.floor((100 / totalQuestions) * 10) / 10;
        const remainder = 100 - baseWeight * totalQuestions;

        updatedQuestions = questions.map((q, index) => ({
          ...q,
          weight: baseWeight + (index === 0 ? remainder : 0),
        }));
      }

      // Ensure exact 100 total by adjusting the last question if needed
      const total = updatedQuestions.reduce((sum, q) => sum + q.weight, 0);
      if (Math.abs(total - 100) > WEIGHT_MARGIN) {
        const diff = 100 - total;
        const lastQuestion = updatedQuestions[updatedQuestions.length - 1];
        updatedQuestions = updatedQuestions.map((q) =>
          q.id === lastQuestion.id ? { ...q, weight: q.weight + diff } : q
        );
      }

      return updatedQuestions;
    },
    []
  );

  const handleAddQuestion = useCallback(
    (pdfId: string) => {
      const newQuestion: Question = {
        id: Math.random().toString(36).substr(2, 9),
        text: "",
        type: "binary",
        weight: 0,
      };

      const updatedEvaluations = evaluationSheet.pdfEvaluations.map(
        (pdfEval) => {
          if (pdfEval.pdfId !== pdfId) return pdfEval;

          const updatedQuestions = [...pdfEval.questions, newQuestion];
          return {
            ...pdfEval,
            questions: distributeWeights(updatedQuestions),
            totalWeight: 100,
          };
        }
      );

      onEvaluationSheetChange({
        ...evaluationSheet,
        pdfEvaluations: updatedEvaluations,
      });
    },
    [evaluationSheet, distributeWeights, onEvaluationSheetChange]
  );

  const handleDeleteQuestion = useCallback(
    (pdfId: string, questionId: string) => {
      const updatedEvaluations = evaluationSheet.pdfEvaluations.map(
        (pdfEval) => {
          if (pdfEval.pdfId !== pdfId) return pdfEval;

          const updatedQuestions = pdfEval.questions.filter(
            (q) => q.id !== questionId
          );
          return {
            ...pdfEval,
            questions: distributeWeights(updatedQuestions),
            totalWeight: updatedQuestions.length ? 100 : 0,
          };
        }
      );

      onEvaluationSheetChange({
        ...evaluationSheet,
        pdfEvaluations: updatedEvaluations,
      });
    },
    [evaluationSheet, distributeWeights, onEvaluationSheetChange]
  );

  const handleQuestionChange = useCallback(
    (pdfId: string, questionId: string, field: keyof Question, value: any) => {
      const updatedEvaluations = evaluationSheet.pdfEvaluations.map(
        (pdfEval) => {
          if (pdfEval.pdfId !== pdfId) return pdfEval;

          if (field === "weight") {
            const newWeight = Math.max(0, Math.min(100, value as number));
            return {
              ...pdfEval,
              questions: distributeWeights(
                pdfEval.questions,
                questionId,
                newWeight
              ),
              totalWeight: 100,
            };
          }

          // Handle non-weight changes
          return {
            ...pdfEval,
            questions: pdfEval.questions.map((q) =>
              q.id === questionId ? { ...q, [field]: value } : q
            ),
          };
        }
      );

      onEvaluationSheetChange({
        ...evaluationSheet,
        pdfEvaluations: updatedEvaluations,
      });
    },
    [evaluationSheet, distributeWeights, onEvaluationSheetChange]
  );

  const totalWeightError = useMemo(() => {
    for (const pdfEval of evaluationSheet.pdfEvaluations) {
      if (pdfEval.questions.length > 0) {
        const total = pdfEval.questions.reduce((sum, q) => sum + q.weight, 0);
        if (Math.abs(total - 100) > WEIGHT_MARGIN) {
          const pdf = pdfs.find((p) => p.id === pdfEval.pdfId);
          return `Total weight for ${
            pdf?.title || "project"
          } must be 100%. Current total: ${total.toFixed(1)}%`;
        }
      }
    }
    return "";
  }, [evaluationSheet.pdfEvaluations, pdfs]);

  // Update parent's error state when weight validation changes
  React.useEffect(() => {
    if (totalWeightError) {
      onEvaluationSheetChange({
        ...evaluationSheet,
        errors: {
          ...evaluationSheet.errors,
          weightValidation: totalWeightError,
        },
      });
    }
  }, [totalWeightError, evaluationSheet, onEvaluationSheetChange]);

  const handleOpenModal = (pdfId: string) => {
    const pdfEval = evaluationSheet.pdfEvaluations.find(
      (pe) => pe.pdfId === pdfId
    );
    if (pdfEval) {
      const weights = pdfEval.questions.reduce((acc, q) => {
        acc[q.id] = q.weight;
        return acc;
      }, {} as { [key: string]: number });
      setTempWeights(weights);
      setSelectedPdfId(pdfId);
      setOpenModal(true);
    }
  };

  const handleSaveWeights = () => {
    if (!selectedPdfId) return;

    const total = Object.values(tempWeights).reduce((sum, w) => sum + w, 0);
    if (Math.abs(total - 100) > WEIGHT_MARGIN) {
      alert("Total weights must sum to 100%");
      return;
    }

    const updatedEvaluations = evaluationSheet.pdfEvaluations.map((pdfEval) => {
      if (pdfEval.pdfId !== selectedPdfId) return pdfEval;

      return {
        ...pdfEval,
        questions: pdfEval.questions.map((q) => ({
          ...q,
          weight: tempWeights[q.id] || q.weight,
        })),
      };
    });

    onEvaluationSheetChange({
      ...evaluationSheet,
      pdfEvaluations: updatedEvaluations,
    });
    setOpenModal(false);
  };

  const handleWeightChange = (questionId: string, value: number) => {
    setTempWeights((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const handleGenerateAIQuestions = async (pdfId: string) => {
    const pdf = pdfs.find(p => p.id === pdfId);
    if (!pdf) return;

    const numQuestions = numQuestionsInput[pdfId] || 5;
    setLoadingAI(prev => ({ ...prev, [pdfId]: true }));

    try {
      const response = await courseService.generateQuestionsFromPDFFile(
        pdf.file,
        pdf.title, 
        pdf.description,
        numQuestions
      );
      
      const aiQuestions = response.generated_questions.questions.map((q: any) => ({
        id: Math.random().toString(36).substr(2, 9),
        text: q.text,
        type: "binary" as const,
        weight: 100 / response.generated_questions.questions.length
      }));

      // Store the original AI questions for refinement
      setPreviousQuestions(prev => ({ ...prev, [pdfId]: aiQuestions }));

      // Update the evaluation sheet with AI questions
      const updatedEvaluations = evaluationSheet.pdfEvaluations.map(pdfEval => {
        if (pdfEval.pdfId === pdfId) {
          return {
            ...pdfEval,
            questions: aiQuestions,
            totalWeight: 100
          };
        }
        return pdfEval;
      });

      onEvaluationSheetChange({
        ...evaluationSheet,
        pdfEvaluations: updatedEvaluations
      });

      setShowRefinement(prev => ({ ...prev, [pdfId]: true }));

    } catch (error) {
      console.error('Error generating AI questions:', error);
      alert('Failed to generate questions. Please try again.');
    } finally {
      setLoadingAI(prev => ({ ...prev, [pdfId]: false }));
    }
  };

  const handleRefineQuestions = async (pdfId: string) => {
    const pdf = pdfs.find(p => p.id === pdfId);
    if (!pdf) return;

    const instructions = refinementInstructions[pdfId];
    if (!instructions?.trim()) {
      alert('Please provide instructions for refining the questions.');
      return;
    }

    const numQuestions = numQuestionsInput[pdfId] || 5;
    const prevQuestions = previousQuestions[pdfId] || [];
    
    setLoadingAI(prev => ({ ...prev, [pdfId]: true }));

    try {
      const response = await courseService.refineQuestionsFromPDFFile(
        pdf.file,
        pdf.title,
        pdf.description,
        numQuestions,
        instructions,
        prevQuestions  // Send the previous questions
      );

      const refinedQuestions = response.generated_questions.questions.map((q: any) => ({
        id: Math.random().toString(36).substr(2, 9),
        text: q.text,
        type: "binary" as const,
        weight: 100 / response.generated_questions.questions.length
      }));

      // Update previous questions for potential further refinement
      setPreviousQuestions(prev => ({ ...prev, [pdfId]: refinedQuestions }));

      // Update with refined questions
      const updatedEvaluations = evaluationSheet.pdfEvaluations.map(pdfEval => {
        if (pdfEval.pdfId === pdfId) {
          return {
            ...pdfEval,
            questions: refinedQuestions,
            totalWeight: 100
          };
        }
        return pdfEval;
      });

      onEvaluationSheetChange({
        ...evaluationSheet,
        pdfEvaluations: updatedEvaluations
      });

      setRefinementInstructions(prev => ({ ...prev, [pdfId]: '' }));

    } catch (error) {
      console.error('Error refining AI questions:', error);
      alert('Failed to refine questions. Please try again.');
    } finally {
      setLoadingAI(prev => ({ ...prev, [pdfId]: false }));
    }
  };

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 2 }}>
          <Typography variant="h6">Evaluation Sheet Management</Typography>
          {totalWeightError ? (
            <Typography color="error" variant="subtitle1">
              {totalWeightError}
            </Typography>
          ) : (
            <Typography color="success.main" variant="subtitle1">
              (✓ All project weights are correctly distributed)
            </Typography>
          )}
        </Box>
        {errors.evaluationSheet && (
          <Typography color="error" sx={{ mb: 2 }}>
            {errors.evaluationSheet}
          </Typography>
        )}
      </Box>

      {pdfs.map((pdf) => {
        const pdfEval = evaluationSheet.pdfEvaluations.find(
          (pe) => pe.pdfId === pdf.id
        ) || {
          pdfId: pdf.id,
          questions: [],
          totalWeight: 0,
        };

        return (
          <Accordion
            key={pdf.id}
            sx={{
              mb: 2,
              background: "rgba(17, 34, 64, 0.95)",
              backdropFilter: "blur(10px)",
              borderRadius: "4px !important",
              "&:before": {
                display: "none",
              },
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                borderBottom: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 2,
                  width: "100%",
                }}
              >
                <Typography variant="subtitle1">{pdf.title}</Typography>
                <Typography
                  variant="body2"
                  sx={{ color: theme.palette.primary.main }}
                >
                  {pdfEval.questions.length} questions - Total weight:{" "}
                  {pdfEval.totalWeight}%
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  gap: 2,
                  mb: 2,
                }}
              >
                <Button
                  variant="contained"
                  startIcon={<PercentIcon />}
                  onClick={() => handleOpenModal(pdf.id)}
                  sx={{
                    background: "linear-gradient(45deg, #7B89F4, #64FFDA)",
                    color: "#0A192F",
                    "&:hover": {
                      background: "linear-gradient(45deg, #A5B4FF, #5A6AD4)",
                    },
                  }}
                >
                  Manage Percentages
                </Button>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={() => handleAddQuestion(pdf.id)}
                  sx={{
                    background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
                    color: "#0A192F",
                    "&:hover": {
                      background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
                    },
                  }}
                >
                  Add Question
                </Button>
                <TextField
                  type="number"
                  label="Questions"
                  size="small"
                  value={numQuestionsInput[pdf.id] || 5}
                  onChange={(e) => setNumQuestionsInput(prev => ({
                    ...prev,
                    [pdf.id]: Math.max(1, Math.min(10, Number(e.target.value)))
                  }))}
                  inputProps={{ min: 1, max: 10 }}
                  sx={{ width: 100 }}
                />
                <Button
                  variant="contained"
                  startIcon={loadingAI[pdf.id] ? <CircularProgress size={20} color="inherit" /> : <AutoAwesomeIcon />}
                  onClick={() => handleGenerateAIQuestions(pdf.id)}
                  disabled={loadingAI[pdf.id]}
                  sx={{
                    background: loadingAI[pdf.id] 
                      ? "linear-gradient(45deg, #9E9E9E, #BDBDBD)" 
                      : "linear-gradient(45deg, #FF6B6B, #FFE66D)",
                    color: "#0A192F",
                    "&:hover": {
                      background: loadingAI[pdf.id] 
                        ? "linear-gradient(45deg, #9E9E9E, #BDBDBD)"
                        : "linear-gradient(45deg, #FF5722, #FFC107)",
                    },
                  }}
                >
                  {loadingAI[pdf.id] ? 'Generating...' : 'Generate with AI'}
                </Button>
              </Box>

              {showRefinement[pdf.id] && (
                <Box sx={{ mt: 2, p: 2, border: '1px solid', borderColor: theme.palette.divider, borderRadius: 1 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    Refine Questions (Optional)
                  </Typography>
                  <TextField
                    fullWidth
                    multiline
                    rows={2}
                    placeholder="e.g., Focus more on technical implementation, Add questions about user experience, Emphasize data analysis aspects..."
                    value={refinementInstructions[pdf.id] || ''}
                    onChange={(e) => setRefinementInstructions(prev => ({
                      ...prev,
                      [pdf.id]: e.target.value
                    }))}
                    sx={{ mb: 1 }}
                  />
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="contained"
                      size="small"
                      onClick={() => handleRefineQuestions(pdf.id)}
                      disabled={loadingAI[pdf.id]}
                      sx={{
                        background: "linear-gradient(45deg, #4CAF50, #8BC34A)",
                        color: "#0A192F",
                      }}
                    >
                      Refine Questions
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setShowRefinement(prev => ({ ...prev, [pdf.id]: false }))}
                    >
                      Skip
                    </Button>
                  </Box>
                </Box>
              )}

              {/* Percentages Modal */}
              <Modal
                open={openModal}
                onClose={() => setOpenModal(false)}
                aria-labelledby="manage-percentages-modal"
              >
                <Box
                  sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: { xs: "90%", sm: 500 },
                    bgcolor: theme.palette.background.paper,
                    boxShadow: 24,
                    p: 4,
                    borderRadius: 2,
                  }}
                >
                  <Typography variant="h6" sx={{ mb: 3 }}>
                    Manage Question Percentages
                  </Typography>

                  {selectedPdfId &&
                    evaluationSheet.pdfEvaluations
                      .find((pe) => pe.pdfId === selectedPdfId)
                      ?.questions.map((question, index) => (
                        <Box key={question.id} sx={{ mb: 3 }}>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mb: 1 }}
                          >
                            Question {index + 1}:{" "}
                            {question.text.substring(0, 50)}...
                          </Typography>
                          <TextField
                            fullWidth
                            type="number"
                            label="Weight (%)"
                            value={tempWeights[question.id] || 0}
                            onChange={(e) =>
                              handleWeightChange(
                                question.id,
                                Number(e.target.value)
                              )
                            }
                            inputProps={{ min: 0, max: 100 }}
                            sx={{ mb: 1 }}
                          />
                        </Box>
                      ))}

                  <Typography color="text.secondary" sx={{ mb: 2 }}>
                    Total:{" "}
                    {Object.values(tempWeights).reduce((sum, w) => sum + w, 0)}%{" "}
                    {Math.abs(
                      Object.values(tempWeights).reduce(
                        (sum, w) => sum + w,
                        0
                      ) - 100
                    ) > WEIGHT_MARGIN && " (Must equal 100%)"}
                  </Typography>

                  <Box
                    sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}
                  >
                    <Button
                      onClick={() => setOpenModal(false)}
                      variant="outlined"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSaveWeights}
                      variant="contained"
                      disabled={
                        Math.abs(
                          Object.values(tempWeights).reduce(
                            (sum, w) => sum + w,
                            0
                          ) - 100
                        ) > WEIGHT_MARGIN
                      }
                    >
                      Save
                    </Button>
                  </Box>
                </Box>
              </Modal>

              {pdfEval.questions.map((question, index) => (
                <Paper
                  key={question.id}
                  sx={{
                    p: 3,
                    mb: 2,
                    background: "rgba(17, 34, 64, 0.95)",
                    backdropFilter: "blur(10px)",
                    borderRadius: 2,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      mb: 2,
                    }}
                  >
                    <Typography variant="h6">Question {index + 1}</Typography>
                    <IconButton
                      onClick={() => handleDeleteQuestion(pdf.id, question.id)}
                      sx={{ color: theme.palette.error.main }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>

                  <TextField
                    fullWidth
                    label="Requirement"
                    placeholder="Describe the requirement that needs to be implemented"
                    value={question.text}
                    onChange={(e) =>
                      handleQuestionChange(
                        pdf.id,
                        question.id,
                        "text",
                        e.target.value
                      )
                    }
                    sx={{ mb: 2 }}
                    helperText="Points will be awarded if requirement is met (evaluator clicks 'Yes')"
                  />

                  <Box
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      gap: 1,
                      mb: 2,
                    }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      Weight: {question.weight}%
                    </Typography>
                    <Slider
                      value={question.weight}
                      onChange={(_, value) =>
                        handleQuestionChange(
                          pdf.id,
                          question.id,
                          "weight",
                          value
                        )
                      }
                      min={0}
                      max={100}
                      step={1}
                      marks={[
                        { value: 0, label: "0%" },
                        { value: 50, label: "50%" },
                        { value: 100, label: "100%" },
                      ]}
                      sx={{
                        width: "100%",
                        color: theme.palette.primary.main,
                        "& .MuiSlider-thumb": {
                          background:
                            "linear-gradient(45deg, #64FFDA, #7B89F4)",
                        },
                        "& .MuiSlider-track": {
                          background:
                            "linear-gradient(45deg, #64FFDA, #7B89F4)",
                        },
                        "& .MuiSlider-rail": {
                          opacity: 0.5,
                        },
                        "& .MuiSlider-mark": {
                          backgroundColor: "#64FFDA",
                        },
                        "& .MuiSlider-markLabel": {
                          color: "text.secondary",
                        },
                      }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      Weights are automatically balanced to sum to 100% within
                      this project
                    </Typography>
                  </Box>
                </Paper>
              ))}
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

export default EvaluationSheetManagement;
