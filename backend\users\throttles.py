from rest_framework.throttling import AnonRateThrottle

class PasswordResetRequestThrottle(AnonRateThrottle):
    """
    Throttle for password reset request endpoint.
    Limits the rate of requests to 5 per hour per IP address.
    """
    rate = '5/hour'
    scope = 'password_reset_request'

class PasswordResetVerifyThrottle(AnonRateThrottle):
    """
    Throttle for password reset verification endpoint.
    Limits the rate of attempts to 10 per hour per IP address.
    """
    rate = '10/hour'
    scope = 'password_reset_verify'

class EmailThrottle(AnonRateThrottle):
    """
    Throttle for email-based operations.
    Limits the rate of email requests to 20 per day per IP address.
    """
    rate = '20/day'
    scope = 'email_operations'
