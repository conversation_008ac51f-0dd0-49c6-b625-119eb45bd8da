from django.core.management.base import BaseCommand
from django.utils import timezone
from projects.models import Course
from projects.serializers import CourseStatisticsSerializer
from services.utils.creating_excel import create_course_statistics_excel
import os

class Command(BaseCommand):
    help = 'Generate Excel reports for all courses'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output-dir',
            type=str,
            default='reports/courses',
            help='Directory where the reports will be saved'
        )

    def handle(self, *args, **options):
        output_dir = options['output_dir']
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')

        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        courses = Course.objects.all()
        total_courses = courses.count()
        
        self.stdout.write(f'Generating reports for {total_courses} courses...')

        for i, course in enumerate(courses, 1):
            try:
                # Generate course statistics
                output_path = os.path.join(output_dir, f'course_{course.code}_report_{timestamp}.xlsx')
                
                # Generate course statistics using the serializer with minimal context
                # Prepare basic course data with safe defaults for None values
                course_stats = {
                    'code': course.code,
                    'name': course.name,
                    'description': course.description,
                    'educator': {
                        'first_name': course.educator.first_name if course.educator else '',
                        'last_name': course.educator.last_name if course.educator else '',
                        'email': course.educator.email if course.educator else ''
                    },
                    'track': {
                        'name': course.track.name if course.track else '',
                        'description': course.track.description if course.track else ''
                    },
                    'created_at': course.created_at.isoformat(),
                    'updated_at': course.updated_at.isoformat()
                }
                
                # Add statistics from serializer
                serializer = CourseStatisticsSerializer(course, context={'for_excel': True})
                stats = serializer.data
                
                # Update course_stats with serializer data
                course_stats.update({
                    'students_count': stats.get('students_count', 0),
                    'projects_count': stats.get('projects_count', 0),
                    'completion_rate': stats.get('completion_rate', 0),
                    'average_score': stats.get('average_score', 0),
                    'active_submissions': stats.get('active_submissions', 0),
                    'completed_submissions': stats.get('completed_submissions', 0),
                    'student_statistics': stats.get('student_statistics', [])
                })
                
                # Create Excel file
                create_course_statistics_excel(course_stats, output_path)
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f'[{i}/{total_courses}] Generated report for course {course.code}: {output_path}'
                    )
                )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'[{i}/{total_courses}] Error generating report for course {course.code}: {str(e)}'
                    )
                )

        self.stdout.write(self.style.SUCCESS(f'Finished generating reports in {output_dir}'))
