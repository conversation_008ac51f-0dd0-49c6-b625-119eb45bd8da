import React, { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  CircularProgress,
  <PERSON><PERSON>,
  useMediaQuery,
} from "@mui/material";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "recharts";
import DownloadIcon from "@mui/icons-material/Download";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import api from "@/services/api";
import { useTheme } from "@mui/material/styles";

interface StudentStats {
  student_name: string;
  student_email: string;
  submissions_count: number;
  completed_projects: number;
  average_score: number;
  current_status: string;
  last_submission: string;
}

interface CourseStats {
  code: string;
  name: string;
  description: string;
  educator: {
    first_name: string;
    last_name: string;
    email: string;
  };
  students_count: number;
  projects_count: number;
  completion_rate: number;
  average_score: number;
  active_submissions: number;
  completed_submissions: number;
  created_at: string;
  updated_at: string;
  student_statistics: StudentStats[];
}

const CourseStatistics: React.FC = () => {
  const theme = useTheme();
  const { courseId } = useParams<{ courseId: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [stats, setStats] = useState<CourseStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  const COLORS = ["#4CAF50", "#FFC107", "#F44336"];

  const handleExportData = async () => {
    if (!stats) return;

    try {
      const response = await api.get(
        `/projects/courses/${courseId}/statistics/excel/`,
        {
          responseType: "blob",
        }
      );

      // Create and download the Excel file
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${stats.name.replace(/\s+/g, "_")}_statistics.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading Excel file:", error);
      // You might want to show a user-friendly error message here
    }
  };

  useEffect(() => {
    const fetchCourseStats = async () => {
      try {
        const response = await api.get(
          `/projects/courses/${courseId}/statistics/`
        );
        setStats(response.data);
      } catch (err) {
        setError("Failed to load course statistics");
        console.error("Error fetching course statistics:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchCourseStats();
  }, [courseId]);

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (error || !stats) {
    return (
      <Box p={3}>
        <Typography color="error">
          {error || "No statistics available"}
        </Typography>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Box
        display="flex"
        flexDirection={{ xs: "column", sm: "row" }}
        justifyContent="space-between"
        alignItems={{ xs: "stretch", sm: "center" }}
        gap={2}
        mb={3}
      >
        <Box display="flex" alignItems="center" gap={2}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate("/educator-dashboard")}
            sx={{
              color: "text.secondary",
              "&:hover": {
                backgroundColor: "rgba(100, 255, 218, 0.1)",
              },
            }}
          >
            Back to Dashboard
          </Button>
          <Typography
            variant={isMobile ? "h5" : "h4"}
            gutterBottom={!isMobile}
            sx={{ textAlign: { xs: "center", sm: "left" } }}
          >
            Course Statistics
          </Typography>
        </Box>
        <Box
          sx={{
            display: "flex",
            justifyContent: { xs: "center", sm: "flex-end" },
          }}
        >
          <Button
            variant="contained"
            startIcon={
              <DownloadIcon sx={{ fontSize: { xs: "1.2rem", sm: "1.5rem" } }} />
            }
            onClick={handleExportData}
            sx={{
              background: "linear-gradient(45deg, #64FFDA, #7B89F4)",
              color: "#0A192F",
              fontSize: { xs: "0.8rem", sm: "1rem" },
              padding: { xs: "6px 12px", sm: "8px 16px" },
              minWidth: { xs: "140px", sm: "180px" },
              "&:hover": {
                background: "linear-gradient(45deg, #5A6AD4, #A5B4FF)",
              },
            }}
          >
            Export as Excel
          </Button>
        </Box>
      </Box>

      <Typography variant="h5" gutterBottom color="primary">
        {stats.name}
      </Typography>
      <Typography variant="subtitle1" gutterBottom>
        {stats.description}
      </Typography>
      <Typography variant="subtitle2" gutterBottom color="textSecondary">
        Educator: {stats.educator.first_name} {stats.educator.last_name}
      </Typography>

      <Grid container spacing={3} mt={2}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%", background: "rgba(17, 34, 64, 0.95)" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Students
              </Typography>
              <Typography variant="h3">{stats.students_count}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%", background: "rgba(17, 34, 64, 0.95)" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Projects
              </Typography>
              <Typography variant="h3">{stats.projects_count}</Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%", background: "rgba(17, 34, 64, 0.95)" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Average Score
              </Typography>
              <Typography variant="h3">
                {stats.average_score.toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: "100%", background: "rgba(17, 34, 64, 0.95)" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Completion Rate
              </Typography>
              <Typography variant="h3">
                {stats.completion_rate.toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={{ xs: 2, sm: 3 }} mt={{ xs: 1, sm: 2 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: "100%", background: "rgba(17, 34, 64, 0.95)" }}>
            <CardContent sx={{ padding: { xs: 2, sm: 3 } }}>
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  fontSize: { xs: "1rem", sm: "1.25rem" },
                  textAlign: { xs: "center", sm: "left" },
                }}
              >
                Submission Status
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={[
                        {
                          name: "Completed",
                          value: stats.completed_submissions,
                        },
                        {
                          name: "Active",
                          value: stats.active_submissions,
                        },
                      ]}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                      label
                    >
                      {COLORS.slice(0, 2).map((color, index) => (
                        <Cell key={`cell-${index}`} fill={color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
              <Box display="flex" justifyContent="center" gap={2} mt={2}>
                <Box display="flex" alignItems="center">
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      backgroundColor: COLORS[0],
                      marginRight: 1,
                    }}
                  />
                  <Typography variant="body2">Completed</Typography>
                </Box>
                <Box display="flex" alignItems="center">
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      backgroundColor: COLORS[1],
                      marginRight: 1,
                    }}
                  />
                  <Typography variant="body2">Active</Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: "100%", background: "rgba(17, 34, 64, 0.95)" }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Course Progress
              </Typography>
              <Box
                sx={{
                  position: "relative",
                  display: "inline-flex",
                  width: "100%",
                  height: 300,
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <CircularProgress
                  variant="determinate"
                  value={stats.completion_rate}
                  size={200}
                  thickness={4}
                  sx={{
                    color: theme.palette.primary.main,
                  }}
                />
                <Box
                  sx={{
                    position: "absolute",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <Typography variant="h4" component="div" color="textPrimary">
                    {`${Math.round(stats.completion_rate)}%`}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CourseStatistics;
