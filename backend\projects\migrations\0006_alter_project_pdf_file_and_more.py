# Generated by Django 4.2.18 on 2025-02-13 16:28

from django.db import migrations, models
import projects.models


class Migration(migrations.Migration):

    dependencies = [
        ('projects', '0005_projectsubmission_assigned_evaluator'),
    ]

    operations = [
        migrations.AlterField(
            model_name='project',
            name='pdf_file',
            field=models.FileField(upload_to=projects.models.get_project_pdf_path, validators=[projects.models.validate_file_size]),
        ),
        migrations.AlterField(
            model_name='projectsubmission',
            name='zip_file',
            field=models.FileField(blank=True, null=True, upload_to=projects.models.get_submission_zip_path, validators=[projects.models.validate_file_size]),
        ),
    ]
