import {
  Box,
  Paper,
  Typography,
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import { useAuth } from "../../contexts/AuthContext";
import { useNavigate, useLocation } from "react-router-dom";
import LogoutIcon from "@mui/icons-material/Logout";
import BackToHome from "@/components/BackToHome";
import { useEffect } from "react";

const PendingApproval = () => {
  const { checkAuth, logout, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const registrationMessage = location.state?.message;

  const handleLogout = async () => {
    logout();
    navigate("/login");
  };

  // Check auth status only once when component mounts
  useEffect(() => {
    checkAuth();
  }, []);

  // Handle navigation based on user state
  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }

    if (user.is_approved && user.is_email_verified) {
      navigate("/dashboard");
    }
  }, [user, navigate]);

  return (
    <Container component="main" maxWidth="sm">
      <BackToHome />
      <Box
        sx={{
          marginTop: 8,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <Paper elevation={3} sx={{ p: 4, width: "100%" }}>
          <Typography component="h1" variant="h4" align="center" gutterBottom>
            Account Verification Status
          </Typography>

          {registrationMessage && (
            <Box sx={{ mt: 2, mb: 3 }}>
              <Alert severity="success">
                <AlertTitle>Registration Complete</AlertTitle>
                {registrationMessage}
              </Alert>
            </Box>
          )}

          <Box sx={{ mt: 2 }}>
            <Alert severity={user?.is_email_verified ? "success" : "info"}>
              <AlertTitle>
                {user?.is_email_verified
                  ? "Email Verified"
                  : "Email Verification Required"}
              </AlertTitle>
              {user?.is_email_verified
                ? "Your email has been successfully verified."
                : "Please check your email and click the verification link to confirm your email address."}
            </Alert>
          </Box>

          {user?.user_type === "E" && (
            <Box sx={{ mt: 2 }}>
              <Alert severity={user?.is_approved ? "success" : "warning"}>
                <AlertTitle>
                  {user?.is_approved
                    ? "Account Approved"
                    : "Admin Approval Required"}
                </AlertTitle>
                {user?.is_approved
                  ? "Your account has been approved by an administrator."
                  : "As an educator, your account requires manual approval by an administrator. This process typically takes 1-2 business days. You will receive an email notification once your account is approved. Please be patient while we verify your credentials."}
              </Alert>
            </Box>
          )}

          {user?.is_email_verified &&
          (user?.user_type === "S" || user?.is_approved) ? (
            <Box sx={{ mt: 3, textAlign: "center" }}>
              <Typography variant="h6" color="success.main" gutterBottom>
                Your account is fully verified!
              </Typography>
              <Button
                variant="contained"
                color="primary"
                onClick={() => navigate("/dashboard")}
                sx={{ mt: 2 }}
              >
                Go to Dashboard
              </Button>
            </Box>
          ) : (
            <Typography variant="body1" sx={{ mt: 3, textAlign: "center" }}>
              You will be automatically redirected to the dashboard once both
              email verification and admin approval are complete.
            </Typography>
          )}
          <Box sx={{ mt: 3, display: "flex", justifyContent: "center" }}>
            <Button
              variant="outlined"
              color="primary"
              onClick={handleLogout}
              startIcon={<LogoutIcon />}
            >
              Logout
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default PendingApproval;
