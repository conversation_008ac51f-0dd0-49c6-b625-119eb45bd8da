# Django imports
from django.core.management.base import BaseCommand
from django.db import transaction

# Local imports
from projects.models import Project, Question, Course, ProjectSubmission, Evaluation
from users.models import Track, User

class Command(BaseCommand):
    help = 'Delete all users (except admin), tracks, courses, projects, submissions, evaluations, and questions from the database'

    @transaction.atomic
    def handle(self, *args, **kwargs):
        # Delete all evaluations
        deleted_evaluations = Evaluation.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_evaluations[0]} evaluations'))

        # Delete all project submissions
        deleted_submissions = ProjectSubmission.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_submissions[0]} project submissions'))

        # Delete all questions
        deleted_questions = Question.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_questions[0]} questions'))

        # Delete all projects
        deleted_projects = Project.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_projects[0]} projects'))

        # Delete all courses
        deleted_courses = Course.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_courses[0]} courses'))

        # Delete all non-admin users
        deleted_users = User.objects.filter(is_superuser=False).delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_users[0]} users'))

        # Delete all tracks
        deleted_tracks = Track.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_tracks[0]} tracks'))

        self.stdout.write(self.style.SUCCESS('Successfully cleared all data while preserving admin users'))
