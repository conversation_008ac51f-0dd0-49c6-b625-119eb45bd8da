import google.generativeai as genai
import json
import logging
from django.conf import settings
from .prompt_templates import EVALUATION_QUESTIONS_PROMPT, EVALUATION_QUESTIONS_REFINEMENT_PROMPT

logger = logging.getLogger(__name__)

class GeminiService:
    """Service for interacting with Gemini API to generate evaluation questions"""
    
    def __init__(self):
        if not hasattr(settings, 'GEMINI_API_KEY') or not settings.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is not configured in settings")
        
        genai.configure(api_key=settings.GEMINI_API_KEY)
        # Use Gemini 2.0 Flash - much better token capacity
        self.model = genai.GenerativeModel('gemini-2.0-flash')
        
    def generate_evaluation_questions(self, project_content, project_title, project_description, 
                                    course_track, num_questions=5):
        """Generate evaluation questions using Gemini API"""
        
        # Iam using setting from Django config , much higher limit also
        max_content_length = getattr(settings, 'AI_MAX_CONTENT_LENGTH', 800000)
        
        if len(project_content) > max_content_length:
            # Only for extremely rare cases
            project_content = project_content[:max_content_length] + "\n\n[Content truncated...]"
        
        prompt = EVALUATION_QUESTIONS_PROMPT.format(
            project_title=project_title,
            project_description=project_description,
            course_track=course_track,
            project_content=project_content,
            num_questions=num_questions
        )
        
        try:
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.3,
                    max_output_tokens=2000,
                    candidate_count=1
                )
            )
            
            response_text = response.text
            questions_data = self.parse_questions_response(response_text)
            
            return questions_data
            
        except Exception as e:
            logger.error(f"Gemini API error: {e}")
            raise Exception(f"Gemini API error: {str(e)}")
    
    def refine_evaluation_questions(self, project_content, project_title, project_description, 
                                  course_track, num_questions=5, user_instructions="", previous_questions=None):
        """Refine evaluation questions using Gemini API"""
        
        # Use setting but leave room for previous questions
        max_content_length = getattr(settings, 'AI_MAX_CONTENT_LENGTH', 800000) - 50000
        
        if len(project_content) > max_content_length:
            project_content = project_content[:max_content_length] + "\n\n[Content truncated...]"
        
        # Format previous questions
        previous_questions_text = ""
        if previous_questions:
            previous_questions_text = "\n".join([f"{i+1}. {q['text']}" for i, q in enumerate(previous_questions)])
        
        prompt = EVALUATION_QUESTIONS_REFINEMENT_PROMPT.format(
            project_title=project_title,
            project_description=project_description,
            course_track=course_track,
            project_content=project_content,
            num_questions=num_questions,
            user_instructions=user_instructions,
            previous_questions=previous_questions_text
        )
        
        try:
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.4,
                    max_output_tokens=2000,
                    candidate_count=1
                )
            )
            
            response_text = response.text
            questions_data = self.parse_questions_response(response_text)
            
            return questions_data
            
        except Exception as e:
            logger.error(f"Gemini API error during refinement: {e}")
            raise Exception(f"Gemini API error: {str(e)}")

    def parse_questions_response(self, response_text):
        """Parse Gemini response into structured questions data"""
        try:
            # Try to parse as JSON first
            data = json.loads(response_text)
            return data
            
        except json.JSONDecodeError:
            logger.warning("Gemini response is not valid JSON, attempting fallback parsing")
            return self._fallback_parse(response_text)
    
    def _fallback_parse(self, text):
        """Fallback parsing method if JSON parsing fails"""
        questions = []
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        for line in lines:
            # Skip empty lines, comments, or structural elements
            if not line or line.startswith(('#', '//', '{', '}', '[', ']')):
                continue
            
            # Skip evaluation criteria lines since we don't use them anymore
            if any(pattern in line.lower() for pattern in ['evaluation_criteria', 'criteria:', 'response_type']):
                continue
            
            question_text = ""
            
            # Case 1: JSON-like field format (text: "question" or "text": "question")
            if any(pattern in line.lower() for pattern in ['text:', '"text":', 'question:']):
                question_text = self._extract_field_value(line, ['text:', '"text":', 'question:'])
            
            # Case 2: Numbered questions (1. question, 2. question, etc.)
            elif any(line.startswith(prefix) for prefix in ['1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.']):
                question_text = self._clean_question_text(line)
            
            # Case 3: Lines that look like questions (contain ? and are substantial)
            elif '?' in line and len(line) > 20:
                question_text = self._clean_question_text(line)
            
            # Case 4: Simple quoted strings that look like questions
            elif ((line.startswith('"') and line.endswith('"')) or 
                  (line.startswith("'") and line.endswith("'"))) and len(line) > 20:
                question_text = self._clean_question_text(line)
            
            # Add question if we found valid text
            if question_text and len(question_text) > 10:
                questions.append({
                    'text': question_text,
                    'response_type': 'boolean'
                })
        
        # Remove duplicates
        unique_questions = self._remove_duplicates(questions)
        
        # DEBUG: Log what we extracted
        logger.info(f"FALLBACK PARSING - Extracted {len(unique_questions)} unique questions")
        for i, q in enumerate(unique_questions):
            logger.info(f"Question {i+1}: {q.get('text', 'NO TEXT')[:100]}...")
        
        return {'questions': unique_questions}
    
    def _extract_field_value(self, line, field_patterns):
        """Extract value from a field line like 'text: "value"' or '"text": "value"'"""
        line_lower = line.lower()
        
        # Find which pattern matches
        for pattern in field_patterns:
            if pattern in line_lower:
                # Find the pattern and extract everything after it
                pattern_index = line_lower.find(pattern)
                value = line[pattern_index + len(pattern):].strip()
                
                # Remove various quote combinations and punctuation
                value = self._clean_quotes_and_punctuation(value)
                
                return value
        
        return ""
    
    def _clean_question_text(self, text):
        """Clean question text by removing prefixes and quotes"""
        # Remove numbered prefixes
        prefixes = ['1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.', 'Q:', 'q:', 'Question:', 'question:']
        
        for prefix in prefixes:
            if text.startswith(prefix):
                text = text[len(prefix):].strip()
                break
        
        return self._clean_quotes_and_punctuation(text)
    
    def _clean_quotes_and_punctuation(self, text):
        """Remove quotes and trailing punctuation from text"""
        text = text.strip()
        
        # Remove multiple layers of quotes
        while True:
            old_text = text
            
            # Remove outer quotes
            if ((text.startswith('"') and text.endswith('"')) or
                (text.startswith("'") and text.endswith("'"))):
                text = text[1:-1].strip()
            
            # Remove trailing commas and semicolons
            text = text.rstrip(',;').strip()
            
            # If no change was made, break the loop
            if text == old_text:
                break
        
        return text
    
    def _remove_duplicates(self, questions):
        """Remove duplicate questions while preserving order"""
        seen = set()
        unique_questions = []
        
        for q in questions:
            question_key = q['text'].lower().strip()
            if question_key not in seen and len(question_key) > 10:
                seen.add(question_key)
                unique_questions.append(q)
        
        return unique_questions