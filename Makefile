.PHONY: backend-up backend-down frontend-up frontend-down up down build logs create-data clear-data

help:
	@echo "Usage: make [command]"
	@echo "Commands:"
	@echo "  up: Start backend services"
	@echo "  down: Stop backend services"
	@echo "  build: Build backend services"
	@echo "  logs: View logs for backend services"
	@echo "  test: Run all tests"
	@echo "  cleanup_test_files: Clean up test files from media directories"

# Backend commands
backend-up:
	cd backend && docker compose up --scale backend=2 -d

backend-down:
	cd backend && docker compose down

backend-build:
	cd backend && docker compose build

backend-logs:
	cd backend && docker compose logs -f

restart-backend:
	cd backend && docker compose restart backend

# Frontend commands
frontend-up:
	cd frontend && npm run dev

frontend-down:
	cd frontend && npm run stop

frontend-build:
	cd frontend && npm run build

frontend-logs:
	cd frontend && npm run logs

# Combined commands
up: backend-up

dev: frontend-up backend-up

re: backend-down backend-build backend-up

rebuild: fclean backend-build backend-up

down: backend-down

build: backend-build

ps:
	docker ps

logs:
	cd backend && docker compose logs -f & cd frontend && docker compose logs -f

fclean:
	cd backend && docker compose down --volumes --remove-orphans

# Data management commands
generate-course-reports:
	docker exec backend-backend-1 python manage.py generate_all_course_reports

create-data:
	docker exec backend-backend-1 python manage.py populate_data

clear-data:
	docker exec backend-backend-1 python manage.py clear_data

migrate:
	cd backend && docker compose up migrations

# Test commands
test:
	docker exec -t backend-backend-1 pytest -v --color=yes

# Run specific test cases
# Usage: make test-one path_to_test_file::TestClassName::test_method_name
# Example: make test-one projects/tests.py::TestQuestionCreateViewAPI::test_create_question_as_educator
test-one:
	docker exec -t backend-backend-1 pytest -v --color=yes "$(filter-out $@,$(MAKECMDGOALS))"

# Clean up test files
cleanup_test_files:
	docker exec -t backend-backend-1 python manage.py cleanup_test_files


EXCEL_FOLDER := /Users/<USER>/Desktop/web_dev_club/backend/users/management/commands/excel_files
DOCKER := $(shell which docker)
ZONE := ATA1
TRACK := AI for University Students

# Check if required tools are available
ifeq ($(DOCKER),)
$(error Docker is not installed or not in PATH)
endif

# Helper to check if excel folder exists
check-excel-folder:
	@test -d $(EXCEL_FOLDER) || (echo "Excel folder does not exist: $(EXCEL_FOLDER)" && exit 1)

# Import the most recent Excel file
import-excel: check-excel-folder
	@if [ -z "$$(ls -A $(EXCEL_FOLDER)/*.xlsx 2>/dev/null)" ]; then \
		echo "No Excel files found in $(EXCEL_FOLDER)"; \
		exit 1; \
	fi; \
	EXCEL_PATH=$$(ls -t $(EXCEL_FOLDER)/*.xlsx | head -n 1); \
	echo "Processing file: $$EXCEL_PATH"; \
	$(DOCKER) cp "$$EXCEL_PATH" backend-backend-1:/tmp/users.xlsx && \
	$(DOCKER) exec backend-backend-1 python manage.py excel_data "/tmp/users.xlsx" "$(ZONE)" "$(TRACK)" || \
	(echo "Failed to process Excel file" && exit 1)

report:
	docker exec -e "FORCE_SEND_EMAILS=true" backend-backend-1 python manage.py create_report

.PHONY: import-excel check-excel-folder