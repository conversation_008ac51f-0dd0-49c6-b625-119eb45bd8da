from django.core.management.base import BaseCommand
from users.models import Zone, Track, User
from django.db import transaction
from django.contrib.auth.hashers import make_password
import openpyxl
import re
import sys
import os

class Command(BaseCommand):
    help = 'Import users from Excel file and assign them to specified zone and track'

    def add_arguments(self, parser):
        parser.add_argument('excel_file', type=str, help='Path to the Excel file')
        parser.add_argument('zone_name', type=str, help='Zone name to assign users to')
        parser.add_argument('track_name', type=str, help='Track name to assign users to')

    def validate_data(self, data):
        required_columns = ['email', 'first_name', 'last_name', 'phone_number']
        headers = [col.lower().strip() for col in data[0]]
        
        # Check for missing columns
        missing_columns = [col for col in required_columns if col not in headers]
        if missing_columns:
            raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
        
        # Get column indices
        col_indices = {col: headers.index(col) for col in required_columns}
        
        validated_data = []
        for row in data[1:]:  # Skip header row
            # Create a dict for the row
            row_dict = {
                col: str(row[col_indices[col]]).strip() if row[col_indices[col]] is not None else ''
                for col in required_columns
            }
            
            # Validate email
            if '@' not in row_dict['email']:
                raise ValueError(f"Invalid email format found: {row_dict['email']}")
            
            # Clean and validate phone number
            phone = row_dict['phone_number']
            if len(phone) == 9 and phone.startswith('50'):
                phone = '0' + phone
            
            if not re.match(r'^050\d{7}$', phone):
                raise ValueError(f"Invalid phone number found: {phone}")
            
            row_dict['phone_number'] = phone
            validated_data.append(row_dict)
        
        return validated_data

    @transaction.atomic
    def handle(self, *args, **options):
        excel_file = options['excel_file']
        zone_name = options['zone_name']
        track_name = options['track_name']
        
        if not os.path.exists(excel_file):
            self.stderr.write(self.style.ERROR(f'File not found: {excel_file}'))
            return

        try:
            zone = Zone.objects.get(name=zone_name)
            track = Track.objects.get(name=track_name)
        except Zone.DoesNotExist:
            self.stderr.write(self.style.ERROR(f'Zone not found: {zone_name}'))
            return
        except Track.DoesNotExist:
            self.stderr.write(self.style.ERROR(f'Track not found: {track_name}'))
            return
        
        try:
            # Read Excel file
            workbook = openpyxl.load_workbook(excel_file)
            worksheet = workbook.active
            
            # Convert worksheet to list of lists
            data = [[cell.value for cell in row] for row in worksheet.rows]
            if not data:
                raise ValueError("The Excel file is empty")
                
            # Validate and clean data
            validated_data = self.validate_data(data)
            
            # Process each row
            for row in validated_data:
                email = row['email']
                username = email.split('@')[0]
                
                try:
                    user, created = User.objects.get_or_create(
                        username=username,
                        defaults={
                            'email': email,
                            'zone': zone,
                            'track': track,
                            'password': make_password('123123'),
                            'is_active': True,
                            'is_approved': True,
                            'is_email_verified': True,
                            'first_name': row['first_name'],
                            'last_name': row['last_name'],
                            'phone_number': row['phone_number'],
                            'points': 2,
                            'level': 1
                        }
                    )
                    
                    if created:
                        self.stdout.write(self.style.SUCCESS(f'Created user: {email}'))
                    else:
                        self.stdout.write(f'User already exists: {email}')
                
                except Exception as e:
                    self.stderr.write(self.style.ERROR(f'Error creating user {email}: {str(e)}'))
            
            self.stdout.write(self.style.SUCCESS('User import completed'))
            
        except Exception as e:
            self.stderr.write(self.style.ERROR(f'Error processing Excel file: {str(e)}'))