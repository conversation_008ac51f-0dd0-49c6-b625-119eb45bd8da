# Standard library imports
import logging
import os
import tempfile
import traceback
from datetime import <PERSON><PERSON><PERSON>
from urllib.parse import urlparse
import json

# Third-party imports
from wsgiref.util import FileWrapper

# Django imports
from django.core.exceptions import ValidationError
from django.core.files import File
from django.urls import reverse
from django.core.files.storage import default_storage
from django.core.handlers.wsgi import WSGIRequest
from django.db import models, transaction
from django.http import Http404, HttpResponse
from django.utils import timezone

# Django REST Framework imports
from rest_framework import generics, permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, FormParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.renderers import <PERSON><PERSON><PERSON>enderer
from rest_framework.response import Response
from rest_framework.views import APIView

# Local imports
from core.permissions import IsApprovedUser
from users.email_utils import send_brevo_email
from users.models import Track, User
from users.serializers import UserSerializer
from .models import Project, ProjectSubmission, Question, Course, CourseRegistrationRequest
from .serializers import (
    ProjectSerializer, ProjectSubmissionSerializer, EvaluationSerializer,
    CourseSerializer, CourseRegistrationRequestSerializer, QuestionSerializer
)

##import the question generator 
from services.ai.question_generator import QuestionGenerator

logger = logging.getLogger(__name__)

class ProjectListCreateView(generics.ListCreateAPIView):
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)

    def get_queryset(self):
        user = self.request.user
        course_code = self.kwargs.get('course_code')
        if not course_code:
            return Project.objects.none()
        try:
            course = Course.objects.get(code=course_code)
        except Course.DoesNotExist:
            return Project.objects.none()

        # Allow access if user is either enrolled in the course OR is the educator of the course
        if course not in user.enrolled_courses.all() and course.educator != user:
            return Project.objects.none()

        return Project.objects.filter(course=course).order_by('-created_at').distinct()

    def create(self, request, *args, **kwargs):
        # Only educators can create projects
        if request.user.user_type != 'E':
            return Response({"detail": "Only educators can create projects"}, status=status.HTTP_403_FORBIDDEN)
            
        course_code = self.kwargs.get('course_code')
        if not course_code:
            return Response({"detail": "Course code is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            course = Course.objects.get(code=course_code)
        except Course.DoesNotExist:
            return Response({"detail": "Course not found"}, status=status.HTTP_404_NOT_FOUND)

        # Only the educator of the course can create projects
        if course.educator != request.user:
            return Response({"detail": "Only the course educator can create projects"}, status=status.HTTP_403_FORBIDDEN)

        # Inject course into serializer data
        data = request.data.copy()
        data['course'] = course.pk  # Use pk or code depending on your Course PK
        serializer = self.get_serializer(data=data)
        if serializer.is_valid():
            serializer.save(course=course)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class QuestionCreateView(generics.CreateAPIView):
    serializer_class = QuestionSerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        course_code = self.kwargs.get('course_code')
        project_id = self.kwargs.get('project_id')
        
        if not project_id:
            return Response({"detail": "Project ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({"detail": "Project not found"}, status=status.HTTP_404_NOT_FOUND)
            
        # Check if the project belongs to the course specified in the URL
        if course_code and project.course.code != course_code:
            return Response(
                {"detail": "Project does not belong to the specified course"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        # Only the educator of the course can create questions
        if project.course.educator != request.user:
            return Response({"detail": "Only the course educator can create questions"}, status=status.HTTP_403_FORBIDDEN)

        # Create a mutable copy of the request data
        data = request.data.copy()
        data['project'] = project.id
        
        serializer = self.get_serializer(data=data)
        if serializer.is_valid():
            serializer.save(project=project)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    

class AIQuestionGeneratorView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]  # Add file upload support
    
    def post(self, request, course_code=None, project_id=None):
        """Generate AI questions - either from existing project or uploaded PDF"""
        
        # Only educators can generate questions
        if request.user.user_type != 'E':
            return Response({"detail": "Only educators can generate questions"}, status=status.HTTP_403_FORBIDDEN)
        
        try:
            generator = QuestionGenerator()
            
            # Get parameters from request
            num_questions = int(request.data.get('num_questions', 5))
            
            # Validate parameters
            if num_questions < 1 or num_questions > 10:
                return Response({"detail": "num_questions must be between 1 and 10"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Case 1: Generate from existing project
            if course_code and project_id:
                try:
                    project = Project.objects.get(id=project_id, course__code=course_code)
                except Project.DoesNotExist:
                    return Response({"detail": "Project not found"}, status=status.HTTP_404_NOT_FOUND)
                
                # Check if educator owns the course
                if project.course.educator != request.user:
                    return Response({"detail": "Only course educator can generate questions"}, status=status.HTTP_403_FORBIDDEN)
                
                generated_questions = generator.generate_questions_for_project(
                    project=project,
                    num_questions=num_questions
                )
            
            # Case 2: Generate from uploaded PDF file
            else:
                pdf_file = request.FILES.get('pdf_file')
                if not pdf_file:
                    return Response({"detail": "PDF file is required"}, status=status.HTTP_400_BAD_REQUEST)
                
                project_title = request.data.get('project_title', '')
                project_description = request.data.get('project_description', '')
                
                # Create temporary file to save PDF
                with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                    for chunk in pdf_file.chunks():
                        temp_file.write(chunk)
                    temp_file_path = temp_file.name
                
                # Check file size and set expectations
                if pdf_file.size > 5 * 1024 * 1024:  # 5MB threshold
                    # Large file detected
                    response_message = "Large PDF detected. Processing in chunks - this may take a bit longer but will provide comprehensive results."
                else:
                    response_message = "Questions generated successfully."
                
                try:
                    generated_questions = generator.generate_questions_from_pdf_file(
                        pdf_path=temp_file_path,
                        project_title=project_title,
                        project_description=project_description,
                        num_questions=num_questions
                    )
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
            
            return Response({
                "success": True,
                "generated_questions": generated_questions,
                "message": response_message
            })
            
        except ValueError as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error generating questions: {e}")
            return Response({"detail": "An error occurred while generating questions"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class AIQuestionRefinementView(APIView):
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request):
        """Refine AI-generated questions based on user feedback"""
        
        # Only educators can refine questions
        if request.user.user_type != 'E':
            return Response({"detail": "Only educators can refine questions"}, status=status.HTTP_403_FORBIDDEN)
        
        try:
            generator = QuestionGenerator()
            
            # Get parameters from request
            pdf_file = request.FILES.get('pdf_file')
            if not pdf_file:
                return Response({"detail": "PDF file is required"}, status=status.HTTP_400_BAD_REQUEST)
            
            project_title = request.data.get('project_title', '')
            project_description = request.data.get('project_description', '')
            num_questions = int(request.data.get('num_questions', 5))
            user_instructions = request.data.get('user_instructions', '')
            
            # Parse previous questions (JSON string from frontend)
            previous_questions_json = request.data.get('previous_questions', '[]')
            try:
                previous_questions = json.loads(previous_questions_json)
            except json.JSONDecodeError:
                previous_questions = []
            
            # Validation
            if num_questions < 1 or num_questions > 10:
                return Response({"detail": "num_questions must be between 1 and 10"}, status=status.HTTP_400_BAD_REQUEST)
            
            if not user_instructions.strip():
                return Response({"detail": "User instructions are required for refinement"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Create temporary file to save PDF
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                for chunk in pdf_file.chunks():
                    temp_file.write(chunk)
                temp_file_path = temp_file.name
            
            try:
                generated_questions = generator.refine_questions_from_pdf_file(
                    pdf_path=temp_file_path,
                    project_title=project_title,
                    project_description=project_description,
                    num_questions=num_questions,
                    user_instructions=user_instructions,
                    previous_questions=previous_questions
                )
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
            
            return Response({
                "success": True,
                "generated_questions": generated_questions,
                "message": "Questions refined successfully based on your feedback."
            })
            
        except ValueError as e:
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error refining questions: {e}")
            return Response({"detail": "An error occurred while refining questions"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CancellableFileUploadMixin:
    renderer_classes = [JSONRenderer]
    format_kwarg = None

    def get_error_response(self, request, message, status_code):
        """Helper method to create error responses"""
        response = Response({"detail": message}, status=status_code)
        response.accepted_renderer = JSONRenderer()
        response.accepted_media_type = 'application/json'
        response.renderer_context = {}
        return response

    def is_request_cancelled(self, request):
        try:
            # Only check for explicit cancellation header
            return request.META.get('HTTP_X_CANCEL_UPLOAD') == 'true'
        except Exception as e:
            # Error checking request cancellation, default to False
            return False

class ProjectSubmissionView(CancellableFileUploadMixin, generics.CreateAPIView):
    serializer_class = ProjectSubmissionSerializer
    permission_classes = [IsAuthenticated, IsApprovedUser]
    parser_classes = (MultiPartParser, FormParser)
    
    def dispatch(self, request, *args, **kwargs):
        """Handle request cancellation at the dispatch level"""
        if self.is_request_cancelled(request):
            response = Response(
                {"detail": "Upload cancelled by user"},
                status=status.HTTP_400_BAD_REQUEST
            )
            response.accepted_renderer = JSONRenderer()
            response.accepted_media_type = 'application/json'
            response.renderer_context = {}
            return response
        return super().dispatch(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        logger.info("=== PROJECT SUBMISSION STARTED ===")

        # Check for cancellation first
        if self.is_request_cancelled(request):
            return Response(
                {"detail": "Upload cancelled by user"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Debug logging
            logger.info(f"Project submission request data: {dict(request.data)}")
            logger.info(f"Request FILES: {list(request.FILES.keys())}")

            # Basic validation
            project_id = request.data.get('project_id')
            if not project_id:
                logger.error("Project ID is missing from request data")
                return Response(
                    {"detail": "Project ID is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            submission_type = request.data.get('submission_type')
            if submission_type not in ['github', 'zip']:
                return Response(
                    {"detail": "Invalid submission type"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate submission based on type
            if submission_type == 'github':
                project_url = request.data.get('github_repo')
                if not project_url:
                    return Response(
                        {"detail": "Project URL is required"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Add https:// if no protocol is specified
                if not project_url.startswith(('http://', 'https://')):
                    project_url = f'https://{project_url}'

                # Validate URL format
                try:
                    result = urlparse(project_url)
                    if not result.netloc:
                        return Response(
                            {"detail": "Invalid URL. Please provide a valid website URL."}, 
                            status=status.HTTP_400_BAD_REQUEST
                        )
                except Exception:
                    return Response(
                        {"detail": "Invalid URL. Please provide a valid website URL."}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:  # zip file
                zip_file = request.FILES.get('zip_file')
                if not zip_file:
                    return Response(
                        {"detail": "ZIP file is required"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                if not zip_file.name.lower().endswith('.zip'):
                    return Response(
                        {"detail": "Only ZIP files are allowed"}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            try:
                with transaction.atomic():
                    # Check for cancellation again
                    if self.is_request_cancelled(request):
                        # If it's a file upload, delete the temporary file
                        if submission_type == 'zip' and 'zip_file' in request.FILES:
                            zip_file = request.FILES['zip_file']
                            if hasattr(zip_file, 'temporary_file_path'):
                                os.unlink(zip_file.temporary_file_path())
                        return Response(
                            {"detail": "Upload cancelled by user"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    project = Project.objects.get(id=project_id)
                    user = User.objects.select_for_update().get(id=request.user.id)
                    
                    # Check prerequisite completion requirement
                    prerequisite = project.prerequisite
                    if prerequisite:
                        has_completed = ProjectSubmission.objects.filter(
                            submitted_by=user,
                            project=prerequisite,
                            status='completed'
                        ).exists()
                        if not has_completed:
                            return Response(
                                {"detail": f"You must complete the prerequisite project: {prerequisite.title} before submitting this project."},
                                status=status.HTTP_403_FORBIDDEN
                            )

                    # Check again if request was cancelled
                    if self.is_request_cancelled(request):
                        return Response(
                            {"detail": "Upload cancelled by user"},
                            status=status.HTTP_400_BAD_REQUEST
                        )
                    
                    # Check if user has a pending or in_evaluation submission for this project
                    # First, clean up any cancelled submissions
                    ProjectSubmission.objects.filter(
                        project=project,
                        submitted_by=user,
                        status='cancelled'
                    ).delete()

                    # Then check for active submissions
                    existing_submission = ProjectSubmission.objects.filter(
                        project=project, 
                        submitted_by=user,
                        status__in=['pending', 'in_evaluation']
                    ).exists()

                    if existing_submission:
                        return Response(
                            {"detail": "You already have a pending submission for this project"}, 
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Check points requirement for evaluation
                    if user.points < project.points_required:
                        return Response(
                            {"detail": f"You need {project.points_required} evaluation points to submit this project for evaluation"}, 
                            status=status.HTTP_403_FORBIDDEN
                        )
                    
                    # Prepare data for serializer - let it handle project_id
                    serializer_data = request.data.copy()

                    # Handle file upload for zip submissions
                    if submission_type == 'zip' and 'zip_file' in request.FILES:
                        # The file is already in request.FILES, serializer will handle it
                        pass
                    
                    # Check for cancellation before processing
                    if self.is_request_cancelled(request):
                        return Response(
                            {"detail": "Upload cancelled by user"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Create and validate serializer
                    serializer = self.get_serializer(data=request.data)
                    serializer.is_valid(raise_exception=True)
                    
                    # Final cancellation check before saving
                    if self.is_request_cancelled(request):
                        return Response(
                            {"detail": "Upload cancelled by user"},
                            status=status.HTTP_400_BAD_REQUEST
                        )

                    # Find available evaluators in the same course
                    available_evaluators = User.objects.filter(
                        enrolled_courses=project.course,  # Same course
                        is_approved=True,  # Must be approved to evaluate
                        is_staff=False,  # Exclude staff/admin users
                        is_superuser=False,  # Exclude superusers
                        user_type='S'  # Only students can evaluate
                    ).exclude(
                        id=request.user.id  # Exclude the submitter
                    ).exclude(
                        submitted_projects__project=project,  # Exclude users who submitted this project
                        submitted_projects__status__in=['pending', 'in_evaluation']  # and their submission is active
                    ).exclude(
                        # Exclude users who have active assignments
                        assigned_submissions__status__in=['pending', 'in_evaluation']
                    )

                    if not available_evaluators.exists():
                        return Response(
                            {"detail": "No evaluators available in the course at the moment. Please try again later."},
                            status=status.HTTP_503_SERVICE_UNAVAILABLE
                        )

                    # Get a random evaluator
                    assigned_evaluator = available_evaluators.order_by('?').first()

                    # Try to notify the assigned evaluator
                    try:
                        send_brevo_email(
                            to_emails=assigned_evaluator.email,
                            subject=f"New Project Submission for Evaluation: {project.title}",
                            html_content=f"""
                            <h1>New Project Submission</h1>
                            <p>You have been assigned to evaluate a new project submission:</p>
                            <ul>
                                <li><strong>Project:</strong> {project.title}</li>
                                <li><strong>Student:</strong> {request.user.username}</li>
                                <li><strong>Course:</strong> {project.course.name}</li>
                            </ul>
                            <p>Please log in to the platform to review and evaluate the submission.</p>
                            """
                        )
                    except Exception as e:
                        logger.error(f"Failed to send evaluator notification: {str(e)}")
                        # Continue with submission creation even if email fails

                    # Create submission with assigned evaluator
                    submission = serializer.save(
                        submitted_by=user,
                        assigned_evaluator=assigned_evaluator
                    )

                    # Deduct evaluation points
                    user.points -= project.points_required
                    user.save()

                    # Return submission with evaluator information
                    response_data = ProjectSubmissionSerializer(submission).data
                    return Response({
                        **response_data,
                        'evaluations_required': project.required_evaluators,
                        'evaluations_completed': 0
                    }, status=status.HTTP_201_CREATED)
                    
            except Project.DoesNotExist:
                return Response(
                    {"detail": "Project not found"}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            except User.DoesNotExist:
                return Response(
                    {"detail": "User not found"}, 
                    status=status.HTTP_404_NOT_FOUND
                )
            except ValidationError as e:
                return Response(
                    {"detail": str(e)}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

        except Exception as e:
            logger.error(f"Error in project submission: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return Response(
                {"detail": "An error occurred while processing your submission. Please ensure your submission follows the required format."},
                status=status.HTTP_400_BAD_REQUEST
            )

class ProjectDetailUpdateView(generics.RetrieveUpdateAPIView):
    """
    Combined view for retrieving and updating a project.
    - GET: Retrieve project details (accessible to students and educators)
    - PUT/PATCH: Update project (accessible only to educators)
    """
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated]
    parser_classes = (MultiPartParser, FormParser)
    lookup_url_kwarg = 'project_id' # our def_queryset limits self.get_object() to the projects 
                                    # within the course, but lookup_url_kwarg filters that list 
                                    # of course-projects to the project with project_id from the url
    
    def get_queryset(self):
        user = self.request.user
        course_code = self.kwargs.get('course_code')

        # For both educators and students, allow viewing all projects in their courses
        # This lets students see the projects they're attempting to modify, so we can
        # return a proper 403 instead of 404
        if user.user_type == 'E':
            # Educators can see all projects for courses they teach
            queryset = Project.objects.filter(course__educator=user)
        else:
            # Students can see projects for courses they're enrolled in
            queryset = Project.objects.filter(course__in=user.enrolled_courses.all())

        # If course_code is provided in the URL, filter by that course
        if course_code:
            queryset = queryset.filter(course__code=course_code)
            
        return queryset

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # Check if the project belongs to the course specified in the URL
        course_code = self.kwargs.get('course_code')
        if course_code and instance.course.code != course_code:
            return Response(
                {"detail": "Project does not belong to the specified course"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        # For the PATCH method, the DRF sets partial in kwargs to true. So what the line below is saying, if partial exists and it's true, then pop that 
        # value and assign it to the partial variable. If it doesn't exist like for the PUT method, just assign the default value of False. After that, we 
        # send this partial variable to the serializer to indicate whether this is a full update or a partial update. When partial=True, 
        # the serializer won't raise validation errors if required fields are missing.
        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Check if user is the educator for this course
        if instance.course.educator != request.user:
            return Response(
                {"detail": "Only the course educator can update this project"}, 
                status=status.HTTP_403_FORBIDDEN
            )

        # Check if the project belongs to the course specified in the URL
        course_code = self.kwargs.get('course_code')
        if course_code and instance.course.code != course_code:
            return Response(
                {"detail": "Project does not belong to the specified course"}, 
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
 
        return Response(serializer.data)

class EvaluationPoolView(generics.ListAPIView):
    serializer_class = ProjectSubmissionSerializer
    permission_classes = [IsAuthenticated, IsApprovedUser]

    def get_queryset(self):
        user = self.request.user

        # First, get submissions assigned to this user
        assigned_submissions = ProjectSubmission.objects.filter(
            assigned_evaluator=user,
            status__in=['pending', 'in_evaluation']
        ).exclude(
            evaluations__evaluator=user  # Exclude if already evaluated
        )

        if assigned_submissions.exists():
            return assigned_submissions

        # If no assignments, try to get a new random assignment
        with transaction.atomic():
            # Lock all unassigned submissions to prevent race conditions
            available_submissions = ProjectSubmission.objects.select_for_update().filter(
                status__in=['pending', 'in_evaluation'],
                assigned_evaluator__isnull=True,  # Not assigned to anyone
                project__course__in=user.enrolled_courses.all()  # Only submissions from courses the evaluator is enrolled in
            ).exclude(
                submitted_by=user  # Not own submissions
            ).exclude(
                evaluations__evaluator=user  # Not already evaluated
            ).exclude(
                # Exclude submissions that have all required evaluations
                id__in=ProjectSubmission.objects.annotate(
                    evaluation_count=models.Count('evaluations')
                ).filter(
                    evaluation_count__gte=models.F('project__required_evaluators')
                ).values('id')
            ).select_related('project', 'submitted_by').order_by('?').first()

            if available_submissions:
                # Assign this submission to the current user
                available_submissions.assigned_evaluator = user
                available_submissions.save()
                return ProjectSubmission.objects.filter(id=available_submissions.id)

        return ProjectSubmission.objects.none()

class EvaluationView(generics.CreateAPIView):
    serializer_class = EvaluationSerializer
    permission_classes = [IsAuthenticated, IsApprovedUser]

    def perform_create(self, serializer):
        try:
            with transaction.atomic():
                submission = ProjectSubmission.objects.select_for_update().get(pk=self.kwargs['pk'])

                # Check if this submission is assigned to the current user
                if submission.assigned_evaluator != self.request.user:
                    raise ValidationError("This submission is not assigned to you for evaluation")

                # Check if evaluator and submitter are both enrolled in the same course as the project
                course = submission.project.course
                if (course not in self.request.user.enrolled_courses.all() or
                    course not in submission.submitted_by.enrolled_courses.all()):
                    raise ValidationError("Both evaluator and submitter must be enrolled in the same course as the project")

                if submission.submitted_by == self.request.user:
                    raise ValidationError("You cannot evaluate your own submission")

                # Check if this user has already evaluated this submission
                if submission.evaluations.filter(evaluator=self.request.user).exists():
                    raise ValidationError("You have already evaluated this submission")

                # Check if we already have enough evaluations
                if submission.evaluations.count() >= submission.project.required_evaluators:
                    raise ValidationError("This submission already has the required number of evaluations")

                # Get question responses from serializer
                question_responses = serializer.validated_data.pop('question_responses', [])

                # Calculate total score using weighted questions
                total_score = 0
                for response in question_responses:
                    try:
                        question = Question.objects.get(id=response['question_id'], project=submission.project)
                        if response['response']:  # if answer is yes/true
                            total_score += question.get_weighted_score()
                    except Question.DoesNotExist:
                        raise ValidationError(f"Invalid question ID: {response['question_id']} for this project")

                # Create evaluation
                evaluation = serializer.save(
                    evaluator=self.request.user,
                    submission=submission,
                    score=total_score,
                    is_approved=total_score >= submission.project.passing_score
                )

                # Note: Question responses are now stored in the evaluation itself
                # We don't need to update the Question model's response field
                # as it's a template field, not individual evaluation responses

                # Give points to the evaluator (not the submitter)
                evaluator = User.objects.select_for_update().get(id=self.request.user.id)
                evaluator.points += 1  # Give 1 point for evaluating
                evaluator.save()

                # Get current evaluation count
                current_evaluations = submission.evaluations.count()

                # Calculate and save the current average score
                evaluations = submission.evaluations.all()
                total_score = sum(evaluation.score for evaluation in evaluations)
                avg_score = round(total_score / len(evaluations))
                submission.final_score = avg_score

                # Update submission status based on evaluation count
                if current_evaluations >= submission.project.required_evaluators:
                    if avg_score >= submission.project.passing_score:
                        submission.status = 'completed'
                        # Level up the submitter on completion
                        submitter = User.objects.select_for_update().get(id=submission.submitted_by.id)
                        submitter.increase_level()  # Using the safe level increase method
                    else:
                        submission.status = 'failed'
                else:
                    # Keep it in evaluation until we have all required evaluations
                    submission.status = 'in_evaluation'

                # Save all changes
                submission.save()

                return {
                    'evaluations_completed': current_evaluations,
                    'evaluations_required': submission.project.required_evaluators,
                    'status': submission.status,
                    'final_score': submission.final_score
                }

        except Exception as e:
            print(f"DEBUG: Error in perform_create: {e}")
            traceback.print_exc()
            raise

class EvaluationDetailView(generics.RetrieveAPIView):
    serializer_class = ProjectSubmissionSerializer
    permission_classes = [IsAuthenticated, IsApprovedUser]

    def get_object(self):
        return ProjectSubmission.objects.get(pk=self.kwargs['pk'])

    def get(self, request, *args, **kwargs):
        submission = self.get_object()
        
        # Check if user has already evaluated this submission
        if submission.evaluations.filter(evaluator=request.user).exists():
            return Response(
                {"message": "You have already evaluated this submission"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Check if we already have enough evaluations
        if submission.evaluations.count() >= submission.project.required_evaluators:
            return Response(
                {"message": "This submission already has all required evaluations"},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        # Allow viewing if status is pending or in_evaluation
        if submission.status in ['pending', 'in_evaluation']:
            submission_data = ProjectSubmissionSerializer(submission, context={'request': request}).data
            
            # Get all submissions by the same user
            user_submissions = ProjectSubmission.objects.filter(
                submitted_by=submission.submitted_by
            ).exclude(id=submission.id).order_by('-created_at')
            
            user_submissions_data = ProjectSubmissionSerializer(
                user_submissions, many=True, context={'request': request}
            ).data
            
            return Response({
                'submission': submission_data,
                'user_submissions': user_submissions_data
            })
        
        return Response(
            {"message": "This submission is no longer available for evaluation"},
            status=status.HTTP_400_BAD_REQUEST
        )

class SubmissionStatusView(generics.RetrieveAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = ProjectSubmissionSerializer

    def get_object(self):
        submission_id = self.kwargs['pk']
        submission = ProjectSubmission.objects.get(pk=submission_id)
        
        if submission.submitted_by != self.request.user:
            raise ValidationError("You can only view your own submission status")
            
        return submission

    def get(self, request, *args, **kwargs):
        try:
            submission = self.get_object()
            current_evaluations = submission.evaluations.count()
            remaining_evaluations = submission.project.required_evaluators - current_evaluations
            pic_url = None

            try:
                pic_url = submission.assigned_evaluator.profile_picture.url
            except:
                pass

            # Get assigned evaluator information if one is assigned
            assigned_evaluator = None
            if submission.assigned_evaluator:
                assigned_evaluator = {
                    'first_name': submission.assigned_evaluator.first_name,
                    'last_name': submission.assigned_evaluator.last_name,
                    'email': submission.assigned_evaluator.email,
                    'level': submission.assigned_evaluator.level,
                    'profile_pic': pic_url or '',
                    'phone_number': submission.assigned_evaluator.phone_number or ''
                }
            return Response({
            'status': submission.status,
            'evaluations_completed': current_evaluations,
            'evaluations_remaining': remaining_evaluations,
            'evaluations_required': submission.project.required_evaluators,
            'final_score': submission.final_score,
            'can_resubmit': submission.status in ['failed'] and request.user.points >= submission.project.points_required,
            'assigned_evaluator': assigned_evaluator
        })
        except ProjectSubmission.DoesNotExist:
            return Response(
                {"detail": "Submission not found"},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class UserProjectSubmissionsView(generics.ListAPIView):
    serializer_class = ProjectSubmissionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return ProjectSubmission.objects.filter(submitted_by=self.request.user)

class CancelUploadView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request):
        project_id = request.data.get('project_id')
        
        if not project_id:
            return Response(
                {"detail": "Project ID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Find any in-progress submissions and clean up their files
            submissions = ProjectSubmission.objects.filter(
                project=project_id,
                submitted_by=request.user,
                status__in=['pending', 'processing', 'in_evaluation']
            )
            
            for submission in submissions:
                # Delete the file if it exists
                if submission.submission_type == 'zip' and submission.zip_file:
                    try:
                        # Delete the actual file
                        submission.zip_file.delete(save=False)
                    except Exception as e:
                        print(f"Error deleting file for submission {submission.id}: {e}")
            
            # Now delete the submissions
            submissions.delete()

            return Response(
                {"detail": "Upload cancelled successfully"},
                status=status.HTTP_200_OK
            )
        except Exception as e:
            return Response(
                {"detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class NextProjectView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, course_code=None):
        user = request.user
        if not course_code:
            return Response({'message': 'No course specified'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            course = Course.objects.get(code=course_code)
        except Course.DoesNotExist:
            return Response({'message': 'Course not found'}, status=status.HTTP_404_NOT_FOUND)
        if course not in user.enrolled_courses.all():
            return Response({'message': 'You are not enrolled in this course'}, status=status.HTTP_403_FORBIDDEN)

        # Find all completed project IDs for this user in this course
        completed_project_ids = set(ProjectSubmission.objects.filter(
            submitted_by=user,
            project__course=course,
            status='completed'
        ).values_list('project__id', flat=True))

        # Find all project IDs that have active submissions (pending or in evaluation)
        active_submission_project_ids = set(ProjectSubmission.objects.filter(
            submitted_by=user,
            project__course=course,
            status__in=['pending', 'in_evaluation']
        ).values_list('project__id', flat=True))

        # Find the next available project:
        # 1. Its prerequisite is either None or completed
        # 2. User hasn't completed it yet
        # 3. User doesn't have an active submission for it (pending/in_evaluation)
        next_project = Project.objects.filter(
            course=course
        ).exclude(
            id__in=completed_project_ids
        ).exclude(
            id__in=active_submission_project_ids
        ).filter(
            models.Q(prerequisite__isnull=True) | models.Q(prerequisite__in=completed_project_ids)
        ).first()

        if next_project:
            serializer = ProjectSerializer(next_project, context={'request': request})
            return Response(serializer.data)
        return Response({'message': 'No projects available'}, status=status.HTTP_404_NOT_FOUND)

class ProjectViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated, IsApprovedUser]
    serializer_class = ProjectSerializer

    def get_queryset(self):
        # Only return projects from user's track
        return Project.objects.filter(track=self.request.user.track)

class IsEducator(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and getattr(request.user, 'user_type', None) == 'E'

class CourseListCreateView(generics.ListCreateAPIView):
    serializer_class = CourseSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        if user.user_type == 'E':  # Educator
            print(Course.objects.filter(educator=user), flush=True)
            return Course.objects.filter(educator=user)
        else:
            return user.enrolled_courses.all()

    def create(self, request, *args, **kwargs):
        if request.user.user_type != 'E':
            return Response({"detail": "Only educators can create courses."}, status=status.HTTP_403_FORBIDDEN)
        return super().create(request, *args, **kwargs)

    def perform_create(self, serializer):
        serializer.save(educator=self.request.user)

class CourseDetailView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Course.objects.all()
    serializer_class = CourseSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        try:
            return Course.objects.get(code=self.kwargs['course_code'])
        except Course.DoesNotExist:
            raise Http404("Course not found")

    def patch(self, request, *args, **kwargs):
        try:
            course = self.get_object()
            if course.educator != request.user:
                return Response({'detail': 'Only the educator can update this course.'}, status=403)
            return self.partial_update(request, *args, **kwargs)
        except Http404:
            return Response({'detail': 'Course not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error updating course: {str(e)}")
            return Response({'detail': 'An error occurred while updating the course.'}, status=500)

    def put(self, request, *args, **kwargs):
        try:
            course = self.get_object()
            if course.educator != request.user:
                return Response({'detail': 'Only the educator can update this course.'}, status=403)
            return self.update(request, *args, **kwargs)
        except Http404:
            return Response({'detail': 'Course not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error updating course: {str(e)}")
            return Response({'detail': 'An error occurred while updating the course.'}, status=500)

    def delete(self, request, *args, **kwargs):
        try:
            course = self.get_object()
            if course.educator != request.user:
                return Response({'detail': 'Only the educator can delete this course.'}, status=403)
            return self.destroy(request, *args, **kwargs)
        except Http404:
            return Response({'detail': 'Course not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error deleting course: {str(e)}")
            return Response({'detail': 'An error occurred while deleting the course.'}, status=500)
            
    def retrieve(self, request, *args, **kwargs):
        try:
            return super().retrieve(request, *args, **kwargs)
        except Http404:
            return Response({'detail': 'Course not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error retrieving course: {str(e)}")
            return Response({'detail': 'An error occurred while retrieving the course.'}, status=500)

class CourseRegistrationListView(APIView):
    permission_classes = [IsAuthenticated, IsEducator]

    def get(self, request, course_code):
        try:
            course = Course.objects.get(code=course_code)
            if course.educator != request.user:
                return Response({'detail': 'You are not the educator for this course.'}, status=403)

            # Get registration requests
            requests = CourseRegistrationRequest.objects.filter(course=course)
            requests_serializer = CourseRegistrationRequestSerializer(requests, many=True)

            # Get enrolled students
            enrolled_students = course.enrolled_students.all()
            students_serializer = UserSerializer(enrolled_students, many=True)

            return Response({
                'registration_requests': requests_serializer.data,
                'enrolled_students': students_serializer.data,
                'enrolled_count': enrolled_students.count()
            })
        except Course.DoesNotExist:
            return Response({'detail': 'Course not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error retrieving course registrations: {str(e)}")
            return Response({'detail': 'An error occurred while retrieving course registrations.'}, status=500)


class AllRegistrationRequestsView(APIView):
    """Get all pending registration requests for all courses taught by the educator"""
    permission_classes = [IsAuthenticated, IsEducator]

    def get(self, request):
        try:
            # Get all courses taught by this educator
            educator_courses = Course.objects.filter(educator=request.user)

            # Get all pending registration requests for these courses
            pending_requests = CourseRegistrationRequest.objects.filter(
                course__in=educator_courses,
                status='pending'
            ).select_related('student', 'course').order_by('-created_at')

            serializer = CourseRegistrationRequestSerializer(pending_requests, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving all registration requests: {str(e)}")
            return Response({'detail': 'An error occurred while retrieving registration requests.'}, status=500)

class CourseRegisterView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, course_code):
        try:
            course = Course.objects.get(code=course_code.upper())
        except Course.DoesNotExist:
            return Response({'detail': 'Invalid course code.'}, status=404)

        user = request.user
        if getattr(user, 'user_type', None) != 'S':
            return Response({'detail': 'Only students can register for courses.'}, status=403)

        req, created = CourseRegistrationRequest.objects.get_or_create(student=user, course=course)
        if not created:
            if req.status == 'pending':
                return Response({'detail': 'You have already requested to join this course.'}, status=409)
            elif req.status == 'approved':
                return Response({'detail': 'You are already enrolled in this course.'}, status=409)
            elif req.status == 'rejected':
                req.status = 'pending'
                req.save()

        serializer = CourseRegistrationRequestSerializer(req)
        return Response(serializer.data, status=201)


class CourseRegistrationApproveView(APIView):
    permission_classes = [IsAuthenticated, IsEducator]

    def post(self, request, request_id):
        try:
            reg_request = CourseRegistrationRequest.objects.select_related('course').get(pk=request_id)
            if reg_request.course.educator != request.user:
                return Response({'detail': 'You are not the educator for this course.'}, status=403)
            if reg_request.status == 'approved':
                return Response({'detail': 'Request already approved.'}, status=409)
            reg_request.status = 'approved'
            reg_request.save()
            # Enroll the student
            reg_request.student.enrolled_courses.add(reg_request.course)
            return Response({'detail': 'Student approved and enrolled.'}, status=200)
        except CourseRegistrationRequest.DoesNotExist:
            return Response({'detail': 'Registration request not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error approving course registration: {str(e)}")
            return Response({'detail': 'An error occurred while approving the registration.'}, status=500)

class CourseRegistrationRejectView(APIView):
    permission_classes = [IsAuthenticated, IsEducator]

    def post(self, request, request_id):
        try:
            reg_request = CourseRegistrationRequest.objects.select_related('course').get(pk=request_id)
            if reg_request.course.educator != request.user:
                return Response({'detail': 'You are not the educator for this course.'}, status=403)
            if reg_request.status == 'rejected':
                return Response({'detail': 'Request already rejected.'}, status=409)
            reg_request.status = 'rejected'
            reg_request.save()
            return Response({'detail': 'Student registration rejected.'}, status=200)
        except CourseRegistrationRequest.DoesNotExist:
            return Response({'detail': 'Registration request not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error rejecting course registration: {str(e)}")
            return Response({'detail': 'An error occurred while rejecting the registration.'}, status=500)

class CourseStudentUnregisterView(APIView):
    permission_classes = [IsAuthenticated, IsEducator]

    def delete(self, request, course_code, student_id):
        try:
            course = Course.objects.get(code=course_code)
            if course.educator != request.user:
                return Response({'detail': 'You are not the educator for this course.'}, status=403)

            student = User.objects.get(id=student_id)
            if course not in student.enrolled_courses.all():
                return Response({'detail': 'Student is not enrolled in this course.'}, status=404)

            # Remove student from course
            student.enrolled_courses.remove(course)

            # Optionally, you might want to update any related registration request status
            try:
                reg_request = CourseRegistrationRequest.objects.get(student=student, course=course)
                reg_request.status = 'rejected'  # or create a new status like 'unregistered'
                reg_request.save()
            except CourseRegistrationRequest.DoesNotExist:
                pass  # No registration request exists, which is fine

            return Response({'detail': 'Student successfully unregistered from course.'}, status=200)
        except Course.DoesNotExist:
            return Response({'detail': 'Course not found.'}, status=404)
        except User.DoesNotExist:
            return Response({'detail': 'Student not found.'}, status=404)
        except Exception as e:
            logger.error(f"Error unregistering student from course: {str(e)}")
            return Response({'detail': 'An error occurred while unregistering the student.'}, status=500)


class BulkRegistrationActionView(APIView):
    """Handle bulk approve/reject actions for registration requests"""
    permission_classes = [IsAuthenticated, IsEducator]

    def post(self, request):
        try:
            action = request.data.get('action')  # 'approve' or 'reject'
            request_ids = request.data.get('request_ids', [])

            if action not in ['approve', 'reject']:
                return Response({'detail': 'Invalid action. Must be "approve" or "reject".'}, status=400)

            if not request_ids:
                return Response({'detail': 'No request IDs provided.'}, status=400)

            # Get all requests and verify educator ownership
            requests = CourseRegistrationRequest.objects.select_related('course', 'student').filter(
                id__in=request_ids,
                course__educator=request.user
            )

            if len(requests) != len(request_ids):
                return Response({'detail': 'Some requests not found or you are not authorized.'}, status=403)

            # Perform bulk action
            updated_count = 0
            enrolled_count = 0

            for reg_request in requests:
                if reg_request.status == 'pending':
                    # Set proper status based on action
                    reg_request.status = 'approved' if action == 'approve' else 'rejected'
                    reg_request.save()
                    updated_count += 1

                    # If approving, enroll the student
                    if action == 'approve':
                        reg_request.student.enrolled_courses.add(reg_request.course)
                        enrolled_count += 1

            message = f'{updated_count} requests {action}d successfully.'
            if action == 'approve':
                message += f' {enrolled_count} students enrolled.'

            return Response({'detail': message, 'updated_count': updated_count}, status=200)

        except Exception as e:
            logger.error(f"Error in bulk registration action: {str(e)}")
            return Response({'detail': 'An error occurred while processing bulk action.'}, status=500)
