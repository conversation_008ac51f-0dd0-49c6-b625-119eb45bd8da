import pytest
import shutil
import os
import stat
from pathlib import Path
from django.conf import settings

@pytest.fixture(autouse=True)
def setup_test_environment(tmp_path, django_db_setup):
    """Set up test environment with local file storage."""
    # Ensure we're using FileSystemStorage for tests
    settings.USE_SPACES = False
    
    # Create test media directory
    media_test_dir = Path(settings.MEDIA_ROOT)
    media_test_dir.mkdir(parents=True, exist_ok=True)
    
    yield
    
    # Cleanup test media files after test
    if media_test_dir.exists():
        try:
            # Try to fix permissions before deletion
            def handle_remove_readonly(func, path, _exc_info):
                """Error handler for shutil.rmtree to handle permission errors"""
                if os.path.exists(path):
                    # Make the file writable and try again
                    os.chmod(path, stat.S_IWRITE | stat.S_IREAD)
                    func(path)

            shutil.rmtree(media_test_dir, onerror=handle_remove_readonly)
        except Exception as e:
            # If cleanup still fails, just log it and continue
            print(f"Warning: Could not clean up test media directory: {e}")
            # Try to at least make the directory writable for next time
            try:
                for root, dirs, files in os.walk(media_test_dir):
                    for d in dirs:
                        os.chmod(os.path.join(root, d), stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
                    for f in files:
                        os.chmod(os.path.join(root, f), stat.S_IWRITE | stat.S_IREAD)
            except Exception:
                pass  # If this fails too, just give up
