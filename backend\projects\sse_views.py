import json
import time
from django.http import StreamingHttpResponse
from django.views import View
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from django.contrib.auth import get_user_model
from .models import CourseRegistrationRequest
from .serializers import CourseRegistrationRequestSerializer
import logging

User = get_user_model()

logger = logging.getLogger(__name__)

@method_decorator(csrf_exempt, name='dispatch')
class RegistrationNotificationSSEView(View):
    """
    Server-Sent Events endpoint for real-time registration notifications
    """
    
    def get(self, request):
        # Handle token authentication for SSE
        user = self.authenticate_user(request)
        if not user:
            return StreamingHttpResponse(
                "data: {\"error\": \"Authentication required\"}\n\n",
                content_type='text/plain',
                status=401
            )

        # Only educators should receive registration notifications
        if user.user_type != 'E':
            return StreamingHttpResponse(
                "data: {\"error\": \"Only educators can receive registration notifications\"}\n\n",
                content_type='text/plain',
                status=403
            )
        
        def event_stream():
            """Generator function that yields SSE formatted data"""
            try:
                # Send initial connection confirmation
                yield f"data: {json.dumps({'type': 'connected', 'message': 'Connected to registration notifications'})}\n\n"
                
                last_check = time.time()
                last_count = self.get_pending_count(request.user)
                
                while True:
                    try:
                        current_time = time.time()
                        
                        # Check for updates every 5 seconds
                        if current_time - last_check >= 5:
                            current_count = self.get_pending_count(request.user)

                            # If count changed, send update
                            if current_count != last_count:
                                pending_requests = self.get_pending_requests(request.user)
                                
                                data = {
                                    'type': 'update',
                                    'count': current_count,
                                    'requests': pending_requests,
                                    'timestamp': current_time
                                }
                                
                                yield f"data: {json.dumps(data)}\n\n"
                                last_count = current_count
                            
                            last_check = current_time
                        
                        # Send heartbeat every 30 seconds
                        if int(current_time) % 30 == 0:
                            yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': current_time})}\n\n"
                        
                        time.sleep(1)  # Check every second but only query DB every 5 seconds
                        
                    except Exception as e:
                        logger.error(f"Error in SSE stream: {str(e)}")
                        yield f"data: {json.dumps({'type': 'error', 'message': 'Stream error occurred'})}\n\n"
                        break
                        
            except Exception as e:
                logger.error(f"Error initializing SSE stream: {str(e)}")
                yield f"data: {json.dumps({'type': 'error', 'message': 'Failed to initialize stream'})}\n\n"
        
        response = StreamingHttpResponse(
            event_stream(),
            content_type='text/event-stream'
        )
        
        # Set headers for SSE
        response['Cache-Control'] = 'no-cache'
        response['Connection'] = 'keep-alive'
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Headers'] = 'Cache-Control'
        
        return response

    def authenticate_user(self, request):
        """Authenticate user from token in Authorization header or query parameter"""
        try:
            # Try Authorization header first
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            if auth_header.startswith('Token '):
                token_key = auth_header.split(' ')[1]
            else:
                # Try query parameter as fallback for SSE
                token_key = request.GET.get('token')

            if not token_key:
                return None

            token = Token.objects.select_related('user').get(key=token_key)
            return token.user

        except (Token.DoesNotExist, IndexError):
            return None

    def get_pending_count(self, user):
        """Get count of pending registration requests for educator's courses"""
        try:
            return CourseRegistrationRequest.objects.filter(
                course__educator=user,
                status='pending'
            ).count()
        except Exception as e:
            logger.error(f"Error getting pending count: {str(e)}")
            return 0
    
    def get_pending_requests(self, user):
        """Get pending registration requests for educator's courses"""
        try:
            requests = CourseRegistrationRequest.objects.filter(
                course__educator=user,
                status='pending'
            ).select_related('student', 'course').order_by('-created_at')[:10]  # Limit to 10 most recent
            
            serializer = CourseRegistrationRequestSerializer(requests, many=True)
            return serializer.data
        except Exception as e:
            logger.error(f"Error getting pending requests: {str(e)}")
            return []


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_notification_update(request):
    """
    Endpoint to trigger notification updates when registration status changes
    This can be called after approval/rejection to force immediate updates
    """
    try:
        # This endpoint doesn't need to do much since SSE will pick up changes
        # But we can use it to log events or trigger cache invalidation if needed
        
        action = request.data.get('action', 'unknown')
        request_id = request.data.get('request_id')
        
        logger.info(f"Notification update triggered: action={action}, request_id={request_id}")
        
        return Response({
            'status': 'success',
            'message': 'Notification update triggered'
        })
        
    except Exception as e:
        logger.error(f"Error triggering notification update: {str(e)}")
        return Response({
            'status': 'error',
            'message': 'Failed to trigger notification update'
        }, status=500)
