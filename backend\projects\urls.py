from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views
from .sse_views import RegistrationNotificationSSEView, trigger_notification_update

router = DefaultRouter()
router.register(r'projects', views.ProjectViewSet, basename='project')

app_name = 'projects'

urlpatterns = [
    path('', include(router.urls)),
    
    # AI Question Generation
    path('projects/<str:project_id>/generate-questions/', views.AIQuestionGeneratorView.as_view(), name='generate-questions'),
    path('projects/<str:project_id>/refine-questions/', views.AIQuestionRefinementView.as_view(), name='refine-questions'),

    # Project submission and evaluation
    path('projects/<str:project_id>/submit/', views.ProjectSubmissionView.as_view(), name='project-submit'),
    path('submissions/<str:submission_id>/status/', views.SubmissionStatusView.as_view(), name='submission-status'),
    
    # Course registration endpoints
    path('courses/<str:course_code>/register/', views.CourseRegisterView.as_view(), name='course-register'),
    path('courses/<str:course_code>/registrations/', views.CourseRegistrationListView.as_view(), name='course-registrations'),
    path('courses/registrations/<int:request_id>/approve/', views.CourseRegistrationApproveView.as_view(), name='course-registration-approve'),
    path('courses/registrations/<int:request_id>/reject/', views.CourseRegistrationRejectView.as_view(), name='course-registration-reject'),
    path('courses/registrations/pending/', views.AllRegistrationRequestsView.as_view(), name='all-registration-requests'),
    path('courses/registrations/bulk-action/', views.BulkRegistrationActionView.as_view(), name='bulk-registration-action'),
    path('courses/<str:course_code>/unregister/<int:student_id>/', views.CourseStudentUnregisterView.as_view(), name='course-student-unregister'),
    
    # SSE endpoints for real-time notifications
    path('courses/registrations/notifications/stream/', RegistrationNotificationSSEView.as_view(), name='registration-notifications-sse'),
    path('courses/registrations/notifications/trigger/', trigger_notification_update, name='trigger-notification-update'),
]
