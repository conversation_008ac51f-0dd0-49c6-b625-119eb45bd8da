import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Typo<PERSON>,
  Box,
  Button,
  Chip,
  Avatar,
  useTheme,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Email as EmailIcon,
  School as SchoolIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  AccessTime as TimeIcon,
} from '@mui/icons-material';
import api from '../../services/api';

export interface RegistrationRequest {
  id: number;
  student: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
  };
  course: {
    code: string;
    name: string;
    description: string;
  };
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
  student_name: string;
  student_email: string;
}

interface RegistrationRequestCardProps {
  request: RegistrationRequest;
  onStatusChange: (requestId: number, newStatus: 'approved' | 'rejected') => void;
}

const RegistrationRequestCard: React.FC<RegistrationRequestCardProps> = ({
  request,
  onStatusChange,
}) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApprove = async () => {
    setLoading(true);
    setError(null);
    try {
      await api.post(`/projects/courses/registrations/${request.id}/approve/`);
      onStatusChange(request.id, 'approved');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to approve request');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    setLoading(true);
    setError(null);
    try {
      await api.post(`/projects/courses/registrations/${request.id}/reject/`);
      onStatusChange(request.id, 'rejected');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to reject request');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card
      sx={{
        background: 'rgba(17, 34, 64, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: `0 8px 25px rgba(100, 255, 218, 0.1)`,
        },
      }}
    >
      <CardContent sx={{ p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Header with student info */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              mr: 2,
              width: 48,
              height: 48,
            }}
          >
            {getInitials(request.student_name)}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ color: 'text.primary', mb: 0.5 }}>
              {request.student_name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EmailIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {request.student_email}
              </Typography>
            </Box>
          </Box>
          <Chip
            label={request.status}
            size="small"
            sx={{
              backgroundColor: 'rgba(255, 184, 108, 0.1)',
              color: theme.palette.warning.main,
              textTransform: 'capitalize',
            }}
          />
        </Box>

        {/* Course info */}
        <Box sx={{ mb: 2, p: 2, backgroundColor: 'rgba(123, 137, 244, 0.05)', borderRadius: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <SchoolIcon sx={{ fontSize: 18, color: theme.palette.secondary.main, mr: 1 }} />
            <Typography variant="subtitle1" sx={{ color: 'text.primary', fontWeight: 600 }}>
              {request.course.name}
            </Typography>
          </Box>
          <Typography variant="body2" sx={{ color: 'text.secondary', mb: 1 }}>
            Course Code: {request.course.code}
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {request.course.description}
          </Typography>
        </Box>

        {/* Request timestamp */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <TimeIcon sx={{ fontSize: 16, color: 'text.secondary', mr: 1 }} />
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Requested on {formatDate(request.created_at)}
          </Typography>
        </Box>

        {/* Action buttons */}
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            startIcon={loading ? <CircularProgress size={16} /> : <ApproveIcon />}
            onClick={handleApprove}
            disabled={loading || request.status !== 'pending'}
            sx={{
              flex: 1,
              background: 'linear-gradient(45deg, #4CAF50, #66BB6A)',
              '&:hover': {
                background: 'linear-gradient(45deg, #45A049, #5CB85C)',
              },
              '&:disabled': {
                background: 'rgba(76, 175, 80, 0.3)',
              },
            }}
          >
            {loading ? 'Approving...' : 'Approve'}
          </Button>
          <Button
            variant="outlined"
            startIcon={loading ? <CircularProgress size={16} /> : <RejectIcon />}
            onClick={handleReject}
            disabled={loading || request.status !== 'pending'}
            sx={{
              flex: 1,
              borderColor: theme.palette.error.main,
              color: theme.palette.error.main,
              '&:hover': {
                borderColor: theme.palette.error.dark,
                backgroundColor: 'rgba(244, 67, 54, 0.1)',
              },
              '&:disabled': {
                borderColor: 'rgba(244, 67, 54, 0.3)',
                color: 'rgba(244, 67, 54, 0.3)',
              },
            }}
          >
            {loading ? 'Rejecting...' : 'Reject'}
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RegistrationRequestCard;
